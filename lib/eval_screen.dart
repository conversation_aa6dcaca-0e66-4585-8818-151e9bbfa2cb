import 'package:flutter/material.dart';

import 'package:fluttertoast/fluttertoast.dart';

import 'package:lsenglish/utils/speech_evaluation.dart';
import 'package:lsenglish/utils/subtitle/src/core/models.dart';
import 'package:lsenglish/widgets/split_english.dart';
import 'package:lsenglish/model/speech_evaluation_result.dart';
import 'dart:convert';

extension String2Color on String {
  Color toColor() {
    final buffer = StringBuffer();
    if (length == 6 || length == 7) buffer.write('ff');
    buffer.write(replaceFirst('#', ''));
    return Color(int.parse(buffer.toString(), radix: 16));
  }
}

class EvalScreen extends StatefulWidget {
  const EvalScreen({super.key});

  @override
  State<EvalScreen> createState() => _EvalScreenState();
}

class _EvalScreenState extends State<EvalScreen> {
  final SpeechEvaluation speechEval = SpeechEvaluation.instance;

  @override
  void initState() {
    super.initState();
    // 监听状态
    speechEval.isEngineInited.addListener(() {
      setState(() {});
    });
    speechEval.isRecording.addListener(() {
      setState(() {});
    });
    speechEval.resultNotifier.addListener(() async {
      if (!mounted) return;
      setState(() {});
    });
    speechEval.errorNotifier.addListener(() {
      final err = speechEval.errorNotifier.value;
      if (err != null) {
        Fluttertoast.showToast(msg: err);
      }
    });
    // 新增：进入页面时尝试读取本地评测结果
    _loadLastEvalResult();
    speechEval.init();
  }

  Future<void> _loadLastEvalResult() async {
    // 加载assets/json/para.json、sent.json、word.json
    final assetBundle = DefaultAssetBundle.of(context);
    final List<String> files = [
      'assets/json/para.json',
      'assets/json/sent.json',
      'assets/json/word.json',
    ];
    var file = "assets/json/para.json";
    try {
      final jsonStr = await assetBundle.loadString(file);
      final jsonMap = jsonDecode(jsonStr);
      final result = SpeechEvaluationResult.fromJson(jsonMap);
      debugPrint("$file 解析后result类型: ${result.result.runtimeType}");
      // 你可以根据需要将result存入speechEval.resultNotifier
      speechEval.resultNotifier.value = result;
    } catch (e) {
      debugPrint("加载或解析$file 失败: $e");
    }
  }

  @override
  Widget build(BuildContext context) {
    final isEngineInited = speechEval.isEngineInited.value;
    final isRecording = speechEval.isRecording.value;
    final evalResult = speechEval.resultNotifier.value;
    return Scaffold(
      appBar: AppBar(
        title: const Text("Voice"),
      ),
      body: Center(
          child: Padding(
        padding: EdgeInsets.only(left: 10, right: 10),
        child: Column(
          children: <Widget>[
            // 新增：评分高亮UI
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 12.0),
              child: SplitEnglishWidget(
                subtitle: Subtitle(
                    ids: [],
                    start: Duration(),
                    end: Duration(),
                    data: "",
                    targetData: "Android dependency should be downloaded from Maven",
                    nativeData: "Android dependency should be downloaded from Maven"),
                evalResult: evalResult,
                // 可传其它样式参数
              ),
            ),
            Row(mainAxisAlignment: MainAxisAlignment.center, children: [
              Column(
                children: [
                  Row(children: [
                    TextButton(
                        onPressed: isEngineInited && !isRecording
                            ? () async {
                                final granted = await speechEval.checkPermission();
                                if (!granted) {
                                  Fluttertoast.showToast(msg: "请先授权录音和存储权限");
                                  return;
                                }
                                // speechEval.startSentence("Android dependency should be downloaded from Maven");
                                // speechEval.startWord("Android");
                                // speechEval.startPara("Android dependency should be downloaded from Maven");
                              }
                            : null,
                        child: const Text("②开始评测")),
                    SizedBox(width: 50),
                    TextButton(
                        onPressed: isEngineInited && isRecording
                            ? () {
                                speechEval.stop();
                              }
                            : null,
                        child: const Text("③停止评测")),
                  ]),
                  Row(children: [
                    TextButton(
                        onPressed: isEngineInited && isRecording
                            ? () {
                                speechEval.cancel();
                              }
                            : null,
                        child: const Text("④取消评测")),
                    SizedBox(width: 50),
                    TextButton(
                        onPressed: isEngineInited
                            ? () {
                                speechEval.playback();
                              }
                            : null,
                        child: const Text("⑤回放录音"))
                  ]),
                  Row(children: [
                    TextButton(
                        onPressed: isEngineInited
                            ? () {
                                speechEval.getLastRecordPath();
                              }
                            : null,
                        child: const Text("⑥当前录音路径")),
                    SizedBox(width: 50),
                    Row(children: [
                      TextButton(
                          onPressed: isEngineInited
                              ? () async {
                                  await speechEval.dispose();
                                }
                              : null,
                          child: const Text("⑦删除评测引擎")),
                    ]),
                  ]),
                ],
              ),
            ])
          ],
        ),
      )),
    );
  }

  @override
  void dispose() {
    speechEval.dispose();
    super.dispose();
  }
}

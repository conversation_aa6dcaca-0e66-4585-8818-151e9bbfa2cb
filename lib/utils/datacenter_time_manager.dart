import 'package:get_storage/get_storage.dart';
import 'package:lsenglish/utils/log.dart';
import 'dart:async';

import '../net/net.dart';
import 'obs.dart';
import 'toast.dart';

class DataCenterTimeManager {
  DataCenterTimeManager._internal();
  static final DataCenterTimeManager _instance = DataCenterTimeManager._internal();
  factory DataCenterTimeManager() => _instance;

  int _startTime = 0;
  int _endTime = 0;
  int _recordedDuration = 0;
  bool _isPaused = false;
  Timer? _timer;

  static const String _startTimeKey = 'DataCenterTimeManager_startTime';
  static const String _endTimeKey = 'DataCenterTimeManager_endTime';
  static const String _recordedDurationKey = 'DataCenterTimeManager_recordedDuration';
  static const String _localResourceIdKey = 'DataCenterTimeManager_localResourceId';
  static const String _localResourceTypeKey = 'DataCenterTimeManager_localResourceType';
  static const String _currentLsTimesKey = 'DataCenterTimeManager_currentLsTimes';

  Future<void> initialize() async {
    _startTime = GetStorage().read(_startTimeKey) ?? 0;
    _endTime = GetStorage().read(_endTimeKey) ?? 0;
    _recordedDuration = GetStorage().read(_recordedDurationKey) ?? 0;
    logger("DataCenterTimeManager initialize _startTime=$_startTime _endTime=$_endTime _recordedDuration=$_recordedDuration");
    if (_startTime != 0 && _endTime != 0 && _recordedDuration != 0) {
      var resourceId = GetStorage().read(_localResourceIdKey);
      var currentLsTimes = GetStorage().read(_currentLsTimesKey);
      var resourceType = GetStorage().read(_localResourceTypeKey);
      if (resourceId?.isEmpty == true || currentLsTimes == 0) {
        return;
      }
      int actualDuration = _endTime - _startTime;
      var learnDuration = ((actualDuration < _recordedDuration ? actualDuration : _recordedDuration) / 60).round();
      Net.getRestClient().addDataEpisode({
        'resourceId': resourceId,
        'resourceType': resourceType ?? 2,
        'learnDuration': learnDuration,
        'startTime': _startTime,
        'endTime': _endTime,
        'currentLsTimes': currentLsTimes,
      }).then((value) {
        toastInfo("已为你补偿了$learnDuration分钟的数据");
        DataCenterTimeManager().clear();
        ObsUtil().updateDataCenter.value = DateTime.now().millisecond;
      });
    }
  }

  void _startTimer() {
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!_isPaused) {
        _recordedDuration++;
        _updateLocalData();
      }
    });
  }

  void begin(String? localResourceId, int? resourceType, int? currentLsTimes) async {
    GetStorage().write(_localResourceIdKey, localResourceId ?? "");
    GetStorage().write(_localResourceTypeKey, resourceType ?? 2);
    GetStorage().write(_currentLsTimesKey, currentLsTimes ?? 0);
  }

  void resetTime() {
    _startTime = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    _updateLocalData();
    _recordedDuration = 0;
    _isPaused = false;
    _startTimer();
  }

  int getDuration() {
    logger("DataCenterTimeManager getDuration _recordedDuration=$_recordedDuration");
    _timer?.cancel();
    _endTime = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    int actualDuration = _endTime - _startTime;
    return actualDuration < _recordedDuration ? actualDuration : _recordedDuration;
  }

  void clear() {
    logger("DataCenterTimeManager clear");
    _clearLocalData();
  }

  void pause() {
    logger("DataCenterTimeManager pause");
    _isPaused = true;
  }

  void resume() {
    logger("DataCenterTimeManager resume");
    _isPaused = false;
  }

  Future<void> _updateLocalData() async {
    _endTime = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    GetStorage().write(_startTimeKey, _startTime);
    GetStorage().write(_endTimeKey, _endTime);
    GetStorage().write(_recordedDurationKey, _recordedDuration);
  }

  Future<void> _clearLocalData() async {
    GetStorage().remove(_startTimeKey);
    GetStorage().remove(_endTimeKey);
    GetStorage().remove(_recordedDurationKey);
  }

  int get startTime => _startTime;
  int get endTime => _endTime;
}

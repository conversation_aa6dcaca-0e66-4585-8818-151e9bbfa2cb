import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lsenglish/model/speech_evaluation_result.dart';

import 'text_style.dart';

class TextComparisonResult {
  final List<TextSpan> spans;
  final double score;

  TextComparisonResult(this.spans, this.score);
}

TextStyle? getRightCompareStyle({bool isLandscape = false}) {
  return getTargetSubtitleTextStyle(isLandscape: isLandscape).copyWith(color: Get.theme.primaryColor);
}

TextStyle? getErrorCompareStyle({bool isLandscape = false}) {
  return getTargetSubtitleTextStyle(isLandscape: isLandscape).copyWith(color: Get.isDarkMode ? Colors.white : Get.theme.colorScheme.onSecondary);
}

/// 根据评分words高亮和显示分数
TextComparisonResult _lsWordsCompareWithScore<T>(String originalString, List<T> evalWords,
    {required int Function(T) getSkipped, required num? Function(T) getScore, bool isLandscape = false}) {
  final wordRegex = RegExp(r"\b\w+['']?\w*\b");
  List<TextSpan> spans = [];
  int lastIndex = 0;
  int wordIdx = 0;
  int correctCount = 0;
  final matches = wordRegex.allMatches(originalString).toList();

  for (var i = 0; i < matches.length; i++) {
    final match = matches[i];
    if (match.start > lastIndex) {
      spans.add(TextSpan(
        text: originalString.substring(lastIndex, match.start),
        style: getTargetSubtitleTextStyle(isLandscape: isLandscape),
      ));
    }
    final word = match.group(0)!;
    if (wordIdx < evalWords.length) {
      final evalWord = evalWords[wordIdx];
      final score = getScore(evalWord);
      final skipped = getSkipped(evalWord);
      Color color;
      if (skipped == 1) {
        color = Colors.grey;
      } else if (score == null) {
        color = Colors.grey;
      } else if (score >= 80) {
        color = Colors.green;
        correctCount++;
      } else if (score >= 60) {
        color = Colors.blue;
        correctCount++;
      } else {
        color = Colors.red;
      }
      spans.add(TextSpan(
        text: '$word ',
        style: getTargetSubtitleTextStyle(isLandscape: isLandscape).copyWith(color: color),
      ));
      wordIdx++;
    } else {
      spans.add(TextSpan(
        text: '$word ',
        style: getTargetSubtitleTextStyle(isLandscape: isLandscape).copyWith(color: Colors.grey),
      ));
    }
    lastIndex = match.end;
  }
  if (lastIndex < originalString.length) {
    spans.add(TextSpan(
      text: originalString.substring(lastIndex),
      style: getTargetSubtitleTextStyle(isLandscape: isLandscape),
    ));
  }
  double score = (correctCount / (matches.length == 0 ? 1 : matches.length)) * 100;
  return TextComparisonResult(spans, score);
}

TextComparisonResult lsSentWordsCompareWithScore(String originalString, List<SentEvalWord> evalWords, {bool isLandscape = false}) {
  return _lsWordsCompareWithScore<SentEvalWord>(
    originalString,
    evalWords,
    getSkipped: (w) => 0,
    getScore: (w) => w.scores?.overall,
    isLandscape: isLandscape,
  );
}

TextComparisonResult lsParaWordsCompareWithScore(String originalString, List<ParaEvalWordDetail> evalWords, {bool isLandscape = false}) {
  return _lsWordsCompareWithScore<ParaEvalWordDetail>(
    originalString,
    evalWords,
    getSkipped: (w) => w.skipped ?? 0,
    getScore: (w) => w.overall,
    isLandscape: isLandscape,
  );
}

TextComparisonResult lsWordWordsCompareWithScore(String originalString, List<WordEvalWord> evalWords, {bool isLandscape = false}) {
  return _lsWordsCompareWithScore<WordEvalWord>(
    originalString,
    evalWords,
    getSkipped: (w) => 0,
    getScore: (w) => w.scores?['overall'],
    isLandscape: isLandscape,
  );
}

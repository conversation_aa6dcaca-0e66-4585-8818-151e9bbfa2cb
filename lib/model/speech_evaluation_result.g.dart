// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'speech_evaluation_result.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SpeechEvaluationResult _$SpeechEvaluationResultFromJson(
        Map<String, dynamic> json) =>
    SpeechEvaluationResult(
      tokenId: json['tokenId'] as String?,
      refText: json['refText'] as String?,
      audioUrl: json['audioUrl'] as String?,
      dtLastResponse: json['dtLastResponse'] as String?,
      result: json['result'],
      applicationId: json['applicationId'] as String?,
      recordId: json['recordId'] as String?,
      params: json['params'] as Map<String, dynamic>?,
      errId: (json['errId'] as num?)?.toInt(),
      error: json['error'] as String?,
      eof: (json['eof'] as num?)?.toInt(),
      timestamp: json['timestamp'] as String?,
    );

Map<String, dynamic> _$SpeechEvaluationResultToJson(
        SpeechEvaluationResult instance) =>
    <String, dynamic>{
      'tokenId': instance.tokenId,
      'refText': instance.refText,
      'audioUrl': instance.audioUrl,
      'dtLastResponse': instance.dtLastResponse,
      'result': instance.result,
      'applicationId': instance.applicationId,
      'recordId': instance.recordId,
      'params': instance.params,
      'errId': instance.errId,
      'error': instance.error,
      'eof': instance.eof,
      'timestamp': instance.timestamp,
    };

ParaEvalResult _$ParaEvalResultFromJson(Map<String, dynamic> json) =>
    ParaEvalResult(
      pronunciation: json['pronunciation'],
      speed: (json['speed'] as num?)?.toInt(),
      kernelVersion: json['kernel_version'] as String?,
      integrity: json['integrity'],
      resourceVersion: json['resource_version'] as String?,
      warning: (json['warning'] as List<dynamic>?)
          ?.map((e) => EvaluationWarning.fromJson(e as Map<String, dynamic>))
          .toList(),
      sentences: (json['sentences'] as List<dynamic>?)
          ?.map((e) => ParaEvalSentence.fromJson(e as Map<String, dynamic>))
          .toList(),
      rhythm: json['rhythm'],
      overall: json['overall'],
      fluency: json['fluency'],
      duration: json['duration'] as String?,
      numericDuration: json['numeric_duration'],
    );

Map<String, dynamic> _$ParaEvalResultToJson(ParaEvalResult instance) =>
    <String, dynamic>{
      'pronunciation': instance.pronunciation,
      'speed': instance.speed,
      'kernel_version': instance.kernelVersion,
      'integrity': instance.integrity,
      'resource_version': instance.resourceVersion,
      'warning': instance.warning?.map((e) => e.toJson()).toList(),
      'sentences': instance.sentences?.map((e) => e.toJson()).toList(),
      'rhythm': instance.rhythm,
      'overall': instance.overall,
      'fluency': instance.fluency,
      'duration': instance.duration,
      'numeric_duration': instance.numericDuration,
    };

ParaEvalSentence _$ParaEvalSentenceFromJson(Map<String, dynamic> json) =>
    ParaEvalSentence(
      sentence: json['sentence'] as String?,
      overall: json['overall'],
      start: (json['start'] as num?)?.toInt(),
      end: (json['end'] as num?)?.toInt(),
      beginIndex: (json['beginIndex'] as num?)?.toInt(),
      endIndex: (json['endIndex'] as num?)?.toInt(),
      details: (json['details'] as List<dynamic>?)
          ?.map((e) => ParaEvalWordDetail.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$ParaEvalSentenceToJson(ParaEvalSentence instance) =>
    <String, dynamic>{
      'sentence': instance.sentence,
      'overall': instance.overall,
      'start': instance.start,
      'end': instance.end,
      'beginIndex': instance.beginIndex,
      'endIndex': instance.endIndex,
      'details': instance.details?.map((e) => e.toJson()).toList(),
    };

ParaEvalWordDetail _$ParaEvalWordDetailFromJson(Map<String, dynamic> json) =>
    ParaEvalWordDetail(
      word: json['word'] as String?,
      overall: json['overall'],
      prominence: (json['prominence'] as num?)?.toInt(),
      charType: (json['charType'] as num?)?.toInt(),
      readType: (json['readType'] as num?)?.toInt(),
      skipped: (json['skipped'] as num?)?.toInt(),
      start: (json['start'] as num?)?.toInt(),
      end: (json['end'] as num?)?.toInt(),
      wordParts: (json['word_parts'] as List<dynamic>?)
          ?.map((e) => EvaluationWordPart.fromJson(e as Map<String, dynamic>))
          .toList(),
      scores: json['scores'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$ParaEvalWordDetailToJson(ParaEvalWordDetail instance) =>
    <String, dynamic>{
      'word': instance.word,
      'overall': instance.overall,
      'prominence': instance.prominence,
      'charType': instance.charType,
      'readType': instance.readType,
      'skipped': instance.skipped,
      'start': instance.start,
      'end': instance.end,
      'word_parts': instance.wordParts?.map((e) => e.toJson()).toList(),
      'scores': instance.scores,
    };

SentEvalResult _$SentEvalResultFromJson(Map<String, dynamic> json) =>
    SentEvalResult(
      duration: json['duration'] as String?,
      fluency: json['fluency'],
      integrity: json['integrity'],
      kernelVersion: json['kernel_version'] as String?,
      numericDuration: json['numeric_duration'],
      overall: json['overall'],
      pauseCount: (json['pause_count'] as num?)?.toInt(),
      pronunciation: json['pronunciation'],
      rearTone: json['rear_tone'] as String?,
      resourceVersion: json['resource_version'] as String?,
      rhythm: json['rhythm'],
      speed: (json['speed'] as num?)?.toInt(),
      warning: (json['warning'] as List<dynamic>?)
          ?.map((e) => EvaluationWarning.fromJson(e as Map<String, dynamic>))
          .toList(),
      words: (json['words'] as List<dynamic>?)
          ?.map((e) => SentEvalWord.fromJson(e as Map<String, dynamic>))
          .toList(),
      liaison: (json['liaison'] as List<dynamic>?)
          ?.map((e) => SentEvalLiaison.fromJson(e as Map<String, dynamic>))
          .toList(),
      plosion: (json['plosion'] as List<dynamic>?)
          ?.map((e) => SentEvalPlosion.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$SentEvalResultToJson(SentEvalResult instance) =>
    <String, dynamic>{
      'duration': instance.duration,
      'fluency': instance.fluency,
      'integrity': instance.integrity,
      'kernel_version': instance.kernelVersion,
      'numeric_duration': instance.numericDuration,
      'overall': instance.overall,
      'pause_count': instance.pauseCount,
      'pronunciation': instance.pronunciation,
      'rear_tone': instance.rearTone,
      'resource_version': instance.resourceVersion,
      'rhythm': instance.rhythm,
      'speed': instance.speed,
      'warning': instance.warning?.map((e) => e.toJson()).toList(),
      'words': instance.words?.map((e) => e.toJson()).toList(),
      'liaison': instance.liaison?.map((e) => e.toJson()).toList(),
      'plosion': instance.plosion?.map((e) => e.toJson()).toList(),
    };

SentEvalWord _$SentEvalWordFromJson(Map<String, dynamic> json) => SentEvalWord(
      charType: (json['charType'] as num?)?.toInt(),
      linkable: (json['linkable'] as num?)?.toInt(),
      linkable_type: (json['linkable_type'] as num?)?.toInt(),
      linked: (json['linked'] as num?)?.toInt(),
      pause: json['pause'] == null
          ? null
          : SentEvalPause.fromJson(json['pause'] as Map<String, dynamic>),
      phonemes: (json['phonemes'] as List<dynamic>?)
          ?.map((e) => SentEvalPhoneme.fromJson(e as Map<String, dynamic>))
          .toList(),
      phonics: (json['phonics'] as List<dynamic>?)
          ?.map((e) => SentEvalPhonic.fromJson(e as Map<String, dynamic>))
          .toList(),
      scores: json['scores'] == null
          ? null
          : SentEvalWordScores.fromJson(json['scores'] as Map<String, dynamic>),
      span: json['span'] == null
          ? null
          : SentEvalSpan.fromJson(json['span'] as Map<String, dynamic>),
      word: json['word'] as String?,
      word_parts: (json['word_parts'] as List<dynamic>?)
          ?.map((e) => SentEvalWordPart.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$SentEvalWordToJson(SentEvalWord instance) =>
    <String, dynamic>{
      'charType': instance.charType,
      'linkable': instance.linkable,
      'linkable_type': instance.linkable_type,
      'linked': instance.linked,
      'pause': instance.pause?.toJson(),
      'phonemes': instance.phonemes?.map((e) => e.toJson()).toList(),
      'phonics': instance.phonics?.map((e) => e.toJson()).toList(),
      'scores': instance.scores?.toJson(),
      'span': instance.span?.toJson(),
      'word': instance.word,
      'word_parts': instance.word_parts?.map((e) => e.toJson()).toList(),
    };

SentEvalPause _$SentEvalPauseFromJson(Map<String, dynamic> json) =>
    SentEvalPause(
      duration: (json['duration'] as num?)?.toInt(),
      type: (json['type'] as num?)?.toInt(),
    );

Map<String, dynamic> _$SentEvalPauseToJson(SentEvalPause instance) =>
    <String, dynamic>{
      'duration': instance.duration,
      'type': instance.type,
    };

SentEvalPhoneme _$SentEvalPhonemeFromJson(Map<String, dynamic> json) =>
    SentEvalPhoneme(
      phoneme: json['phoneme'] as String?,
      pronunciation: (json['pronunciation'] as num?)?.toInt(),
      span: json['span'] == null
          ? null
          : SentEvalSpan.fromJson(json['span'] as Map<String, dynamic>),
      stress_mark: (json['stress_mark'] as num?)?.toInt(),
    );

Map<String, dynamic> _$SentEvalPhonemeToJson(SentEvalPhoneme instance) =>
    <String, dynamic>{
      'phoneme': instance.phoneme,
      'pronunciation': instance.pronunciation,
      'span': instance.span,
      'stress_mark': instance.stress_mark,
    };

SentEvalPhonic _$SentEvalPhonicFromJson(Map<String, dynamic> json) =>
    SentEvalPhonic(
      overall: (json['overall'] as num?)?.toInt(),
      phoneme:
          (json['phoneme'] as List<dynamic>?)?.map((e) => e as String).toList(),
      spell: json['spell'] as String?,
    );

Map<String, dynamic> _$SentEvalPhonicToJson(SentEvalPhonic instance) =>
    <String, dynamic>{
      'overall': instance.overall,
      'phoneme': instance.phoneme,
      'spell': instance.spell,
    };

SentEvalWordScores _$SentEvalWordScoresFromJson(Map<String, dynamic> json) =>
    SentEvalWordScores(
      overall: (json['overall'] as num?)?.toInt(),
      prominence: (json['prominence'] as num?)?.toInt(),
      pronunciation: (json['pronunciation'] as num?)?.toInt(),
    );

Map<String, dynamic> _$SentEvalWordScoresToJson(SentEvalWordScores instance) =>
    <String, dynamic>{
      'overall': instance.overall,
      'prominence': instance.prominence,
      'pronunciation': instance.pronunciation,
    };

SentEvalSpan _$SentEvalSpanFromJson(Map<String, dynamic> json) => SentEvalSpan(
      start: (json['start'] as num?)?.toInt(),
      end: (json['end'] as num?)?.toInt(),
    );

Map<String, dynamic> _$SentEvalSpanToJson(SentEvalSpan instance) =>
    <String, dynamic>{
      'start': instance.start,
      'end': instance.end,
    };

SentEvalWordPart _$SentEvalWordPartFromJson(Map<String, dynamic> json) =>
    SentEvalWordPart(
      beginIndex: (json['beginIndex'] as num?)?.toInt(),
      charType: (json['charType'] as num?)?.toInt(),
      endIndex: (json['endIndex'] as num?)?.toInt(),
      part: json['part'] as String?,
    );

Map<String, dynamic> _$SentEvalWordPartToJson(SentEvalWordPart instance) =>
    <String, dynamic>{
      'beginIndex': instance.beginIndex,
      'charType': instance.charType,
      'endIndex': instance.endIndex,
      'part': instance.part,
    };

WordEvalResult _$WordEvalResultFromJson(Map<String, dynamic> json) =>
    WordEvalResult(
      overall: json['overall'],
      pronunciation: json['pronunciation'],
      kernelVersion: json['kernel_version'] as String?,
      resourceVersion: json['resource_version'] as String?,
      words: (json['words'] as List<dynamic>?)
          ?.map((e) => WordEvalWord.fromJson(e as Map<String, dynamic>))
          .toList(),
      duration: json['duration'] as String?,
      numericDuration: json['numeric_duration'],
      pauseCount: (json['pause_count'] as num?)?.toInt(),
      stress: json['stress'],
      warning: (json['warning'] as List<dynamic>?)
          ?.map((e) => EvaluationWarning.fromJson(e as Map<String, dynamic>))
          .toList(),
      rhythm: json['rhythm'],
      fluency: json['fluency'],
      integrity: json['integrity'],
      speed: (json['speed'] as num?)?.toInt(),
    );

Map<String, dynamic> _$WordEvalResultToJson(WordEvalResult instance) =>
    <String, dynamic>{
      'overall': instance.overall,
      'pronunciation': instance.pronunciation,
      'kernel_version': instance.kernelVersion,
      'resource_version': instance.resourceVersion,
      'words': instance.words?.map((e) => e.toJson()).toList(),
      'duration': instance.duration,
      'numeric_duration': instance.numericDuration,
      'pause_count': instance.pauseCount,
      'stress': instance.stress,
      'warning': instance.warning?.map((e) => e.toJson()).toList(),
      'rhythm': instance.rhythm,
      'fluency': instance.fluency,
      'integrity': instance.integrity,
      'speed': instance.speed,
    };

WordEvalWord _$WordEvalWordFromJson(Map<String, dynamic> json) => WordEvalWord(
      word: json['word'] as String?,
      charType: (json['charType'] as num?)?.toInt(),
      readType: (json['readType'] as num?)?.toInt(),
      pause: json['pause'] == null
          ? null
          : EvaluationPause.fromJson(json['pause'] as Map<String, dynamic>),
      phonemes: (json['phonemes'] as List<dynamic>?)
          ?.map((e) => WordEvalPhoneme.fromJson(e as Map<String, dynamic>))
          .toList(),
      phonics: (json['phonics'] as List<dynamic>?)
          ?.map((e) => WordEvalPhonic.fromJson(e as Map<String, dynamic>))
          .toList(),
      scores: json['scores'] as Map<String, dynamic>?,
      span: json['span'] == null
          ? null
          : EvaluationSpan.fromJson(json['span'] as Map<String, dynamic>),
      wordParts: (json['word_parts'] as List<dynamic>?)
          ?.map((e) => EvaluationWordPart.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$WordEvalWordToJson(WordEvalWord instance) =>
    <String, dynamic>{
      'word': instance.word,
      'charType': instance.charType,
      'readType': instance.readType,
      'pause': instance.pause?.toJson(),
      'phonemes': instance.phonemes?.map((e) => e.toJson()).toList(),
      'phonics': instance.phonics?.map((e) => e.toJson()).toList(),
      'scores': instance.scores,
      'span': instance.span?.toJson(),
      'word_parts': instance.wordParts?.map((e) => e.toJson()).toList(),
    };

WordEvalPhoneme _$WordEvalPhonemeFromJson(Map<String, dynamic> json) =>
    WordEvalPhoneme(
      phoneme: json['phoneme'] as String?,
      pronunciation: (json['pronunciation'] as num?)?.toInt(),
      span: json['span'] == null
          ? null
          : EvaluationSpan.fromJson(json['span'] as Map<String, dynamic>),
      stress_mark: (json['stress_mark'] as num?)?.toInt(),
    );

Map<String, dynamic> _$WordEvalPhonemeToJson(WordEvalPhoneme instance) =>
    <String, dynamic>{
      'phoneme': instance.phoneme,
      'pronunciation': instance.pronunciation,
      'span': instance.span,
      'stress_mark': instance.stress_mark,
    };

WordEvalPhonic _$WordEvalPhonicFromJson(Map<String, dynamic> json) =>
    WordEvalPhonic(
      overall: (json['overall'] as num?)?.toInt(),
      phoneme:
          (json['phoneme'] as List<dynamic>?)?.map((e) => e as String).toList(),
      spell: json['spell'] as String?,
    );

Map<String, dynamic> _$WordEvalPhonicToJson(WordEvalPhonic instance) =>
    <String, dynamic>{
      'overall': instance.overall,
      'phoneme': instance.phoneme,
      'spell': instance.spell,
    };

EvaluationWarning _$EvaluationWarningFromJson(Map<String, dynamic> json) =>
    EvaluationWarning(
      code: (json['code'] as num?)?.toInt(),
      message: json['message'] as String?,
    );

Map<String, dynamic> _$EvaluationWarningToJson(EvaluationWarning instance) =>
    <String, dynamic>{
      'code': instance.code,
      'message': instance.message,
    };

EvaluationSpan _$EvaluationSpanFromJson(Map<String, dynamic> json) =>
    EvaluationSpan(
      start: (json['start'] as num?)?.toInt(),
      end: (json['end'] as num?)?.toInt(),
    );

Map<String, dynamic> _$EvaluationSpanToJson(EvaluationSpan instance) =>
    <String, dynamic>{
      'start': instance.start,
      'end': instance.end,
    };

EvaluationWordPart _$EvaluationWordPartFromJson(Map<String, dynamic> json) =>
    EvaluationWordPart(
      part: json['part'] as String?,
      beginIndex: (json['beginIndex'] as num?)?.toInt(),
      endIndex: (json['endIndex'] as num?)?.toInt(),
      charType: (json['charType'] as num?)?.toInt(),
    );

Map<String, dynamic> _$EvaluationWordPartToJson(EvaluationWordPart instance) =>
    <String, dynamic>{
      'part': instance.part,
      'beginIndex': instance.beginIndex,
      'endIndex': instance.endIndex,
      'charType': instance.charType,
    };

EvaluationPause _$EvaluationPauseFromJson(Map<String, dynamic> json) =>
    EvaluationPause(
      duration: (json['duration'] as num?)?.toInt(),
      type: (json['type'] as num?)?.toInt(),
    );

Map<String, dynamic> _$EvaluationPauseToJson(EvaluationPause instance) =>
    <String, dynamic>{
      'duration': instance.duration,
      'type': instance.type,
    };

SentEvalLiaison _$SentEvalLiaisonFromJson(Map<String, dynamic> json) =>
    SentEvalLiaison(
      first: json['first'] == null
          ? null
          : SentEvalLiaisonWord.fromJson(json['first'] as Map<String, dynamic>),
      second: json['second'] == null
          ? null
          : SentEvalLiaisonWord.fromJson(
              json['second'] as Map<String, dynamic>),
      firstPhoneme: json['first_phoneme'] as String?,
      secondPhoneme: json['second_phoneme'] as String?,
      linkableType: (json['linkable_type'] as num?)?.toInt(),
    );

Map<String, dynamic> _$SentEvalLiaisonToJson(SentEvalLiaison instance) =>
    <String, dynamic>{
      'first': instance.first?.toJson(),
      'second': instance.second?.toJson(),
      'first_phoneme': instance.firstPhoneme,
      'second_phoneme': instance.secondPhoneme,
      'linkable_type': instance.linkableType,
    };

SentEvalLiaisonWord _$SentEvalLiaisonWordFromJson(Map<String, dynamic> json) =>
    SentEvalLiaisonWord(
      index: (json['index'] as num?)?.toInt(),
      word: json['word'] as String?,
    );

Map<String, dynamic> _$SentEvalLiaisonWordToJson(
        SentEvalLiaisonWord instance) =>
    <String, dynamic>{
      'index': instance.index,
      'word': instance.word,
    };

SentEvalPlosion _$SentEvalPlosionFromJson(Map<String, dynamic> json) =>
    SentEvalPlosion(
      first: json['first'] == null
          ? null
          : SentEvalLiaisonWord.fromJson(json['first'] as Map<String, dynamic>),
      second: json['second'] == null
          ? null
          : SentEvalLiaisonWord.fromJson(
              json['second'] as Map<String, dynamic>),
      firstPhoneme: json['first_phoneme'] as String?,
      secondPhoneme: json['second_phoneme'] as String?,
      linkableType: (json['linkable_type'] as num?)?.toInt(),
    );

Map<String, dynamic> _$SentEvalPlosionToJson(SentEvalPlosion instance) =>
    <String, dynamic>{
      'first': instance.first?.toJson(),
      'second': instance.second?.toJson(),
      'first_phoneme': instance.firstPhoneme,
      'second_phoneme': instance.secondPhoneme,
      'linkable_type': instance.linkableType,
    };

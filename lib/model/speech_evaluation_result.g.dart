// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'speech_evaluation_result.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SpeechEvaluationResult _$SpeechEvaluationResultFromJson(
        Map<String, dynamic> json) =>
    SpeechEvaluationResult(
      tokenId: json['tokenId'] as String?,
      refText: json['refText'] as String?,
      audioUrl: json['audioUrl'] as String?,
      dtLastResponse: json['dtLastResponse'] as String?,
      result: json['result'],
      applicationId: json['applicationId'] as String?,
      recordId: json['recordId'] as String?,
      params: json['params'] as Map<String, dynamic>?,
      errId: (json['errId'] as num?)?.toInt(),
      error: json['error'] as String?,
      eof: (json['eof'] as num?)?.toInt(),
    );

Map<String, dynamic> _$SpeechEvaluationResultToJson(
        SpeechEvaluationResult instance) =>
    <String, dynamic>{
      'tokenId': instance.tokenId,
      'refText': instance.refText,
      'audioUrl': instance.audioUrl,
      'dtLastResponse': instance.dtLastResponse,
      'result': instance.result,
      'applicationId': instance.applicationId,
      'recordId': instance.recordId,
      'params': instance.params,
      'errId': instance.errId,
      'error': instance.error,
      'eof': instance.eof,
    };

ParaEvalResult _$ParaEvalResultFromJson(Map<String, dynamic> json) =>
    ParaEvalResult(
      pronunciation: json['pronunciation'],
      speed: (json['speed'] as num?)?.toInt(),
      kernelVersion: json['kernel_version'] as String?,
      integrity: json['integrity'],
      resourceVersion: json['resource_version'] as String?,
      warning: (json['warning'] as List<dynamic>?)
          ?.map((e) => ParaEvalWarning.fromJson(e as Map<String, dynamic>))
          .toList(),
      sentences: (json['sentences'] as List<dynamic>?)
          ?.map((e) => ParaEvalSentence.fromJson(e as Map<String, dynamic>))
          .toList(),
      rhythm: json['rhythm'],
      overall: json['overall'],
      fluency: json['fluency'],
      duration: json['duration'] as String?,
      numericDuration: json['numeric_duration'],
    );

Map<String, dynamic> _$ParaEvalResultToJson(ParaEvalResult instance) =>
    <String, dynamic>{
      'pronunciation': instance.pronunciation,
      'speed': instance.speed,
      'kernel_version': instance.kernelVersion,
      'integrity': instance.integrity,
      'resource_version': instance.resourceVersion,
      'warning': instance.warning?.map((e) => e.toJson()).toList(),
      'sentences': instance.sentences?.map((e) => e.toJson()).toList(),
      'rhythm': instance.rhythm,
      'overall': instance.overall,
      'fluency': instance.fluency,
      'duration': instance.duration,
      'numeric_duration': instance.numericDuration,
    };

ParaEvalSentence _$ParaEvalSentenceFromJson(Map<String, dynamic> json) =>
    ParaEvalSentence(
      sentence: json['sentence'] as String?,
      overall: json['overall'],
      start: (json['start'] as num?)?.toInt(),
      end: (json['end'] as num?)?.toInt(),
      beginIndex: (json['beginIndex'] as num?)?.toInt(),
      endIndex: (json['endIndex'] as num?)?.toInt(),
      details: (json['details'] as List<dynamic>?)
          ?.map((e) => ParaEvalWordDetail.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$ParaEvalSentenceToJson(ParaEvalSentence instance) =>
    <String, dynamic>{
      'sentence': instance.sentence,
      'overall': instance.overall,
      'start': instance.start,
      'end': instance.end,
      'beginIndex': instance.beginIndex,
      'endIndex': instance.endIndex,
      'details': instance.details?.map((e) => e.toJson()).toList(),
    };

ParaEvalWordDetail _$ParaEvalWordDetailFromJson(Map<String, dynamic> json) =>
    ParaEvalWordDetail(
      word: json['word'] as String?,
      overall: json['overall'],
      prominence: (json['prominence'] as num?)?.toInt(),
      charType: (json['charType'] as num?)?.toInt(),
      readType: (json['readType'] as num?)?.toInt(),
      skipped: (json['skipped'] as num?)?.toInt(),
      start: (json['start'] as num?)?.toInt(),
      end: (json['end'] as num?)?.toInt(),
      wordParts: (json['wordParts'] as List<dynamic>?)
          ?.map((e) => ParaEvalWordPart.fromJson(e as Map<String, dynamic>))
          .toList(),
      scores: json['scores'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$ParaEvalWordDetailToJson(ParaEvalWordDetail instance) =>
    <String, dynamic>{
      'word': instance.word,
      'overall': instance.overall,
      'prominence': instance.prominence,
      'charType': instance.charType,
      'readType': instance.readType,
      'skipped': instance.skipped,
      'start': instance.start,
      'end': instance.end,
      'wordParts': instance.wordParts?.map((e) => e.toJson()).toList(),
      'scores': instance.scores,
    };

ParaEvalWordPart _$ParaEvalWordPartFromJson(Map<String, dynamic> json) =>
    ParaEvalWordPart(
      part: json['part'] as String?,
      beginIndex: (json['beginIndex'] as num?)?.toInt(),
      endIndex: (json['endIndex'] as num?)?.toInt(),
      charType: (json['charType'] as num?)?.toInt(),
    );

Map<String, dynamic> _$ParaEvalWordPartToJson(ParaEvalWordPart instance) =>
    <String, dynamic>{
      'part': instance.part,
      'beginIndex': instance.beginIndex,
      'endIndex': instance.endIndex,
      'charType': instance.charType,
    };

ParaEvalWarning _$ParaEvalWarningFromJson(Map<String, dynamic> json) =>
    ParaEvalWarning(
      code: (json['code'] as num?)?.toInt(),
      message: json['message'] as String?,
    );

Map<String, dynamic> _$ParaEvalWarningToJson(ParaEvalWarning instance) =>
    <String, dynamic>{
      'code': instance.code,
      'message': instance.message,
    };

SentEvalResult _$SentEvalResultFromJson(Map<String, dynamic> json) =>
    SentEvalResult(
      duration: json['duration'] as String?,
      fluency: (json['fluency'] as num?)?.toInt(),
      integrity: (json['integrity'] as num?)?.toInt(),
      kernelVersion: json['kernel_version'] as String?,
      numericDuration: (json['numeric_duration'] as num?)?.toDouble(),
      overall: (json['overall'] as num?)?.toInt(),
      pauseCount: (json['pause_count'] as num?)?.toInt(),
      pronunciation: (json['pronunciation'] as num?)?.toInt(),
      rearTone: json['rear_tone'] as String?,
      resourceVersion: json['resource_version'] as String?,
      rhythm: (json['rhythm'] as num?)?.toInt(),
      speed: (json['speed'] as num?)?.toInt(),
      warning: (json['warning'] as List<dynamic>?)
          ?.map((e) => SentEvalWarning.fromJson(e as Map<String, dynamic>))
          .toList(),
      words: (json['words'] as List<dynamic>?)
          ?.map((e) => SentEvalWord.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$SentEvalResultToJson(SentEvalResult instance) =>
    <String, dynamic>{
      'duration': instance.duration,
      'fluency': instance.fluency,
      'integrity': instance.integrity,
      'kernel_version': instance.kernelVersion,
      'numeric_duration': instance.numericDuration,
      'overall': instance.overall,
      'pause_count': instance.pauseCount,
      'pronunciation': instance.pronunciation,
      'rear_tone': instance.rearTone,
      'resource_version': instance.resourceVersion,
      'rhythm': instance.rhythm,
      'speed': instance.speed,
      'warning': instance.warning?.map((e) => e.toJson()).toList(),
      'words': instance.words?.map((e) => e.toJson()).toList(),
    };

SentEvalWarning _$SentEvalWarningFromJson(Map<String, dynamic> json) =>
    SentEvalWarning(
      code: (json['code'] as num?)?.toInt(),
      message: json['message'] as String?,
    );

Map<String, dynamic> _$SentEvalWarningToJson(SentEvalWarning instance) =>
    <String, dynamic>{
      'code': instance.code,
      'message': instance.message,
    };

SentEvalWord _$SentEvalWordFromJson(Map<String, dynamic> json) => SentEvalWord(
      charType: (json['charType'] as num?)?.toInt(),
      linkable: (json['linkable'] as num?)?.toInt(),
      linkable_type: (json['linkable_type'] as num?)?.toInt(),
      linked: (json['linked'] as num?)?.toInt(),
      pause: json['pause'] == null
          ? null
          : SentEvalPause.fromJson(json['pause'] as Map<String, dynamic>),
      phonemes: (json['phonemes'] as List<dynamic>?)
          ?.map((e) => SentEvalPhoneme.fromJson(e as Map<String, dynamic>))
          .toList(),
      phonics: (json['phonics'] as List<dynamic>?)
          ?.map((e) => SentEvalPhonic.fromJson(e as Map<String, dynamic>))
          .toList(),
      scores: json['scores'] == null
          ? null
          : SentEvalWordScores.fromJson(json['scores'] as Map<String, dynamic>),
      span: json['span'] == null
          ? null
          : SentEvalSpan.fromJson(json['span'] as Map<String, dynamic>),
      word: json['word'] as String?,
      word_parts: (json['word_parts'] as List<dynamic>?)
          ?.map((e) => SentEvalWordPart.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$SentEvalWordToJson(SentEvalWord instance) =>
    <String, dynamic>{
      'charType': instance.charType,
      'linkable': instance.linkable,
      'linkable_type': instance.linkable_type,
      'linked': instance.linked,
      'pause': instance.pause?.toJson(),
      'phonemes': instance.phonemes?.map((e) => e.toJson()).toList(),
      'phonics': instance.phonics?.map((e) => e.toJson()).toList(),
      'scores': instance.scores?.toJson(),
      'span': instance.span?.toJson(),
      'word': instance.word,
      'word_parts': instance.word_parts?.map((e) => e.toJson()).toList(),
    };

SentEvalPause _$SentEvalPauseFromJson(Map<String, dynamic> json) =>
    SentEvalPause(
      duration: (json['duration'] as num?)?.toInt(),
      type: (json['type'] as num?)?.toInt(),
    );

Map<String, dynamic> _$SentEvalPauseToJson(SentEvalPause instance) =>
    <String, dynamic>{
      'duration': instance.duration,
      'type': instance.type,
    };

SentEvalPhoneme _$SentEvalPhonemeFromJson(Map<String, dynamic> json) =>
    SentEvalPhoneme(
      phoneme: json['phoneme'] as String?,
      pronunciation: (json['pronunciation'] as num?)?.toInt(),
      span: json['span'] == null
          ? null
          : SentEvalSpan.fromJson(json['span'] as Map<String, dynamic>),
      stress_mark: (json['stress_mark'] as num?)?.toInt(),
    );

Map<String, dynamic> _$SentEvalPhonemeToJson(SentEvalPhoneme instance) =>
    <String, dynamic>{
      'phoneme': instance.phoneme,
      'pronunciation': instance.pronunciation,
      'span': instance.span,
      'stress_mark': instance.stress_mark,
    };

SentEvalPhonic _$SentEvalPhonicFromJson(Map<String, dynamic> json) =>
    SentEvalPhonic(
      overall: (json['overall'] as num?)?.toInt(),
      phoneme:
          (json['phoneme'] as List<dynamic>?)?.map((e) => e as String).toList(),
      spell: json['spell'] as String?,
    );

Map<String, dynamic> _$SentEvalPhonicToJson(SentEvalPhonic instance) =>
    <String, dynamic>{
      'overall': instance.overall,
      'phoneme': instance.phoneme,
      'spell': instance.spell,
    };

SentEvalWordScores _$SentEvalWordScoresFromJson(Map<String, dynamic> json) =>
    SentEvalWordScores(
      overall: (json['overall'] as num?)?.toInt(),
      prominence: (json['prominence'] as num?)?.toInt(),
      pronunciation: (json['pronunciation'] as num?)?.toInt(),
    );

Map<String, dynamic> _$SentEvalWordScoresToJson(SentEvalWordScores instance) =>
    <String, dynamic>{
      'overall': instance.overall,
      'prominence': instance.prominence,
      'pronunciation': instance.pronunciation,
    };

SentEvalSpan _$SentEvalSpanFromJson(Map<String, dynamic> json) => SentEvalSpan(
      start: (json['start'] as num?)?.toInt(),
      end: (json['end'] as num?)?.toInt(),
    );

Map<String, dynamic> _$SentEvalSpanToJson(SentEvalSpan instance) =>
    <String, dynamic>{
      'start': instance.start,
      'end': instance.end,
    };

SentEvalWordPart _$SentEvalWordPartFromJson(Map<String, dynamic> json) =>
    SentEvalWordPart(
      beginIndex: (json['beginIndex'] as num?)?.toInt(),
      charType: (json['charType'] as num?)?.toInt(),
      endIndex: (json['endIndex'] as num?)?.toInt(),
      part: json['part'] as String?,
    );

Map<String, dynamic> _$SentEvalWordPartToJson(SentEvalWordPart instance) =>
    <String, dynamic>{
      'beginIndex': instance.beginIndex,
      'charType': instance.charType,
      'endIndex': instance.endIndex,
      'part': instance.part,
    };

WordEvalResult _$WordEvalResultFromJson(Map<String, dynamic> json) =>
    WordEvalResult(
      overall: json['overall'],
      pronunciation: json['pronunciation'],
      kernelVersion: json['kernel_version'] as String?,
      resourceVersion: json['resource_version'] as String?,
      words: (json['words'] as List<dynamic>?)
          ?.map((e) => WordEvalWord.fromJson(e as Map<String, dynamic>))
          .toList(),
      duration: json['duration'] as String?,
      numericDuration: json['numeric_duration'],
      pauseCount: (json['pause_count'] as num?)?.toInt(),
      stress: json['stress'],
      warning: (json['warning'] as List<dynamic>?)
          ?.map((e) => SentEvalWarning.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$WordEvalResultToJson(WordEvalResult instance) =>
    <String, dynamic>{
      'overall': instance.overall,
      'pronunciation': instance.pronunciation,
      'kernel_version': instance.kernelVersion,
      'resource_version': instance.resourceVersion,
      'words': instance.words?.map((e) => e.toJson()).toList(),
      'duration': instance.duration,
      'numeric_duration': instance.numericDuration,
      'pause_count': instance.pauseCount,
      'stress': instance.stress,
      'warning': instance.warning?.map((e) => e.toJson()).toList(),
    };

WordEvalWord _$WordEvalWordFromJson(Map<String, dynamic> json) => WordEvalWord(
      word: json['word'] as String?,
      charType: (json['charType'] as num?)?.toInt(),
      readType: (json['readType'] as num?)?.toInt(),
      pause: json['pause'] == null
          ? null
          : WordEvalPause.fromJson(json['pause'] as Map<String, dynamic>),
      phonemes: (json['phonemes'] as List<dynamic>?)
          ?.map((e) => WordEvalPhoneme.fromJson(e as Map<String, dynamic>))
          .toList(),
      phonics: (json['phonics'] as List<dynamic>?)
          ?.map((e) => WordEvalPhonic.fromJson(e as Map<String, dynamic>))
          .toList(),
      scores: json['scores'] as Map<String, dynamic>?,
      span: json['span'] == null
          ? null
          : WordEvalSpan.fromJson(json['span'] as Map<String, dynamic>),
      wordParts: (json['word_parts'] as List<dynamic>?)
          ?.map((e) => WordEvalWordPart.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$WordEvalWordToJson(WordEvalWord instance) =>
    <String, dynamic>{
      'word': instance.word,
      'charType': instance.charType,
      'readType': instance.readType,
      'pause': instance.pause?.toJson(),
      'phonemes': instance.phonemes?.map((e) => e.toJson()).toList(),
      'phonics': instance.phonics?.map((e) => e.toJson()).toList(),
      'scores': instance.scores,
      'span': instance.span?.toJson(),
      'word_parts': instance.wordParts?.map((e) => e.toJson()).toList(),
    };

WordEvalPause _$WordEvalPauseFromJson(Map<String, dynamic> json) =>
    WordEvalPause(
      duration: (json['duration'] as num?)?.toInt(),
      type: (json['type'] as num?)?.toInt(),
    );

Map<String, dynamic> _$WordEvalPauseToJson(WordEvalPause instance) =>
    <String, dynamic>{
      'duration': instance.duration,
      'type': instance.type,
    };

WordEvalPhoneme _$WordEvalPhonemeFromJson(Map<String, dynamic> json) =>
    WordEvalPhoneme(
      phoneme: json['phoneme'] as String?,
      pronunciation: (json['pronunciation'] as num?)?.toInt(),
      span: json['span'] == null
          ? null
          : WordEvalSpan.fromJson(json['span'] as Map<String, dynamic>),
      stress_mark: (json['stress_mark'] as num?)?.toInt(),
    );

Map<String, dynamic> _$WordEvalPhonemeToJson(WordEvalPhoneme instance) =>
    <String, dynamic>{
      'phoneme': instance.phoneme,
      'pronunciation': instance.pronunciation,
      'span': instance.span,
      'stress_mark': instance.stress_mark,
    };

WordEvalPhonic _$WordEvalPhonicFromJson(Map<String, dynamic> json) =>
    WordEvalPhonic(
      overall: (json['overall'] as num?)?.toInt(),
      phoneme:
          (json['phoneme'] as List<dynamic>?)?.map((e) => e as String).toList(),
      spell: json['spell'] as String?,
    );

Map<String, dynamic> _$WordEvalPhonicToJson(WordEvalPhonic instance) =>
    <String, dynamic>{
      'overall': instance.overall,
      'phoneme': instance.phoneme,
      'spell': instance.spell,
    };

WordEvalSpan _$WordEvalSpanFromJson(Map<String, dynamic> json) => WordEvalSpan(
      start: (json['start'] as num?)?.toInt(),
      end: (json['end'] as num?)?.toInt(),
    );

Map<String, dynamic> _$WordEvalSpanToJson(WordEvalSpan instance) =>
    <String, dynamic>{
      'start': instance.start,
      'end': instance.end,
    };

WordEvalWordPart _$WordEvalWordPartFromJson(Map<String, dynamic> json) =>
    WordEvalWordPart(
      beginIndex: (json['beginIndex'] as num?)?.toInt(),
      charType: (json['charType'] as num?)?.toInt(),
      endIndex: (json['endIndex'] as num?)?.toInt(),
      part: json['part'] as String?,
    );

Map<String, dynamic> _$WordEvalWordPartToJson(WordEvalWordPart instance) =>
    <String, dynamic>{
      'beginIndex': instance.beginIndex,
      'charType': instance.charType,
      'endIndex': instance.endIndex,
      'part': instance.part,
    };

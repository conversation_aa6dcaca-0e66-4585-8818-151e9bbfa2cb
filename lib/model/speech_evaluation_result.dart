import 'package:json_annotation/json_annotation.dart';

part 'speech_evaluation_result.g.dart';

// 新增结构体的JsonSerializable生成声明
// ignore_for_file: non_constant_identifier_names

/// 语音评测通用响应体，result字段根据coreType类型动态解析
@JsonSerializable(explicitToJson: true)
class SpeechEvaluationResult {
  /// 终端用户请求id
  final String? tokenId;
  /// 评测文本
  final String? refText;
  /// 在线音频地址，需设置attachAudioUrl
  final String? audioUrl;
  /// 评分结果返回时间
  final String? dtLastResponse;
  /// 评分结果（类型为ParaEvalResult、SentEvalResult、WordEvalResult，需根据coreType判断）
  final dynamic result;
  /// appKey
  final String? applicationId;
  /// 评分唯一id，建议业务层保存，方便排查错误
  final String? recordId;
  /// 请求参数，需设置getParam
  final Map<String, dynamic>? params;
  /// 错误码，出现该字段时无result字段
  final int? errId;
  /// 错误码信息，出现该字段时无result字段
  final String? error;
  /// 0：中间结果；1：最终结果
  final int? eof;

  SpeechEvaluationResult({
    this.tokenId,
    this.refText,
    this.audioUrl,
    this.dtLastResponse,
    this.result,
    this.applicationId,
    this.recordId,
    this.params,
    this.errId,
    this.error,
    this.eof,
  });

  factory SpeechEvaluationResult.fromJson(Map<String, dynamic> json) {
    final coreType = json['params']?['request']?['coreType'] ?? '';
    dynamic resultObj;
    if (json['result'] != null) {
      if (coreType == 'sent.eval' || coreType == 'sent.eval.pro') {
        resultObj = SentEvalResult.fromJson(json['result']);
      } else if (coreType == 'para.eval') {
        resultObj = ParaEvalResult.fromJson(json['result']);
      } else if (coreType == 'word.eval' || coreType == 'word.eval.pro') {
        resultObj = WordEvalResult.fromJson(json['result']);
      } else {
        resultObj = json['result'];
      }
    }
    return SpeechEvaluationResult(
      tokenId: json['tokenId'],
      refText: json['refText'],
      audioUrl: json['audioUrl'],
      dtLastResponse: json['dtLastResponse'],
      result: resultObj,
      applicationId: json['applicationId'],
      recordId: json['recordId'],
      params: json['params'],
      errId: json['errId'],
      error: json['error'],
      eof: json['eof'],
    );
  }
  Map<String, dynamic> toJson() => _$SpeechEvaluationResultToJson(this);
}

/// para.eval 段落评测结果
@JsonSerializable(explicitToJson: true)
class ParaEvalResult {
  /// 发音得分
  final dynamic pronunciation;
  /// 语速（词/分）
  final int? speed;
  /// 内核版本
  @JsonKey(name: 'kernel_version')
  final String? kernelVersion;
  /// 完整度
  final dynamic integrity;
  /// 资源版本
  @JsonKey(name: 'resource_version')
  final String? resourceVersion;
  /// 音频检测的提示
  final List<ParaEvalWarning>? warning;
  /// 段落详情（句子数组）
  final List<ParaEvalSentence>? sentences;
  /// 韵律度得分
  final dynamic rhythm;
  /// 总分
  final dynamic overall;
  /// 流利度
  final dynamic fluency;
  /// 音频时长（字符串，单位：秒）
  final String? duration;
  /// 音频时长（数值，单位：秒）
  @JsonKey(name: 'numeric_duration')
  final dynamic numericDuration;

  ParaEvalResult({
    this.pronunciation,
    this.speed,
    this.kernelVersion,
    this.integrity,
    this.resourceVersion,
    this.warning,
    this.sentences,
    this.rhythm,
    this.overall,
    this.fluency,
    this.duration,
    this.numericDuration,
  });

  factory ParaEvalResult.fromJson(Map<String, dynamic> json) => _$ParaEvalResultFromJson(json);
  Map<String, dynamic> toJson() => _$ParaEvalResultToJson(this);
}

/// 段落评测-句子详情
@JsonSerializable(explicitToJson: true)
class ParaEvalSentence {
  /// 句子内容
  final String? sentence;
  /// 句子总分
  final dynamic overall;
  /// 句子开始在音轨上的时间（单位：10毫秒）
  final int? start;
  /// 句子结束在音轨上的时间（单位：10毫秒）
  final int? end;
  /// 句子在文本中开始的位置
  final int? beginIndex;
  /// 句子在文本中结束的位置
  final int? endIndex;
  /// 句子中单词的详细信息（需开启paragraph_need_word_score）
  final List<ParaEvalWordDetail>? details;

  ParaEvalSentence({
    this.sentence,
    this.overall,
    this.start,
    this.end,
    this.beginIndex,
    this.endIndex,
    this.details,
  });

  factory ParaEvalSentence.fromJson(Map<String, dynamic> json) => _$ParaEvalSentenceFromJson(json);
  Map<String, dynamic> toJson() => _$ParaEvalSentenceToJson(this);
}

/// 段落评测-单词详情
@JsonSerializable(explicitToJson: true)
class ParaEvalWordDetail {
  /// 单词
  final String? word;
  /// 单词分数
  final dynamic overall;
  /// 单词重读，0=非重读，1=重读
  final int? prominence;
  /// 0=非标点，1=标点
  final int? charType;
  /// 0=正常，3=漏读，4=重复读
  final int? readType;
  /// 是否已读，0=已读，1=未读
  final int? skipped;
  /// 单词在音轨上的开始时间（单位：10毫秒）
  final int? start;
  /// 单词在音轨上的结束时间（单位：10毫秒）
  final int? end;
  /// 单词文本信息
  final List<ParaEvalWordPart>? wordParts;
  /// 单词得分情况（仅readType=4时有）
  final Map<String, dynamic>? scores;

  ParaEvalWordDetail({
    this.word,
    this.overall,
    this.prominence,
    this.charType,
    this.readType,
    this.skipped,
    this.start,
    this.end,
    this.wordParts,
    this.scores,
  });

  factory ParaEvalWordDetail.fromJson(Map<String, dynamic> json) => _$ParaEvalWordDetailFromJson(json);
  Map<String, dynamic> toJson() => _$ParaEvalWordDetailToJson(this);
}

/// 段落评测-单词文本信息
@JsonSerializable()
class ParaEvalWordPart {
  /// 单词
  final String? part;
  /// 单词在文本中开始的位置
  final int? beginIndex;
  /// 单词在文本中结束的位置
  final int? endIndex;
  /// 0=非标点，1=标点
  final int? charType;

  ParaEvalWordPart({this.part, this.beginIndex, this.endIndex, this.charType});

  factory ParaEvalWordPart.fromJson(Map<String, dynamic> json) => _$ParaEvalWordPartFromJson(json);
  Map<String, dynamic> toJson() => _$ParaEvalWordPartToJson(this);
}

/// 段落评测-音频检测提示
@JsonSerializable()
class ParaEvalWarning {
  /// 错误码
  final int? code;
  /// 音频检测的提示信息
  final String? message;

  ParaEvalWarning({this.code, this.message});

  factory ParaEvalWarning.fromJson(Map<String, dynamic> json) => _$ParaEvalWarningFromJson(json);
  Map<String, dynamic> toJson() => _$ParaEvalWarningToJson(this);
}

/// sent.eval/sent.eval.pro 句子评测结果
@JsonSerializable(explicitToJson: true)
class SentEvalResult {
  /// 评测时长（字符串）
  final String? duration;
  /// 流利度
  final int? fluency;
  /// 完整度
  final int? integrity;
  /// 内核版本
  @JsonKey(name: 'kernel_version')
  final String? kernelVersion;
  /// 数值型时长
  @JsonKey(name: 'numeric_duration')
  final double? numericDuration;
  /// 总分
  final int? overall;
  /// 停顿次数
  @JsonKey(name: 'pause_count')
  final int? pauseCount;
  /// 发音得分
  final int? pronunciation;
  /// 句尾语调
  @JsonKey(name: 'rear_tone')
  final String? rearTone;
  /// 资源版本
  @JsonKey(name: 'resource_version')
  final String? resourceVersion;
  /// 节奏
  final int? rhythm;
  /// 语速
  final int? speed;
  /// 警告信息
  final List<SentEvalWarning>? warning;
  /// 单词详情
  final List<SentEvalWord>? words;

  SentEvalResult({
    this.duration,
    this.fluency,
    this.integrity,
    this.kernelVersion,
    this.numericDuration,
    this.overall,
    this.pauseCount,
    this.pronunciation,
    this.rearTone,
    this.resourceVersion,
    this.rhythm,
    this.speed,
    this.warning,
    this.words,
  });

  factory SentEvalResult.fromJson(Map<String, dynamic> json) => _$SentEvalResultFromJson(json);
  Map<String, dynamic> toJson() => _$SentEvalResultToJson(this);
}

@JsonSerializable()
class SentEvalWarning {
  final int? code;
  final String? message;
  SentEvalWarning({this.code, this.message});
  factory SentEvalWarning.fromJson(Map<String, dynamic> json) => _$SentEvalWarningFromJson(json);
  Map<String, dynamic> toJson() => _$SentEvalWarningToJson(this);
}

@JsonSerializable(explicitToJson: true)
class SentEvalWord {
  final int? charType;
  final int? linkable;
  final int? linkable_type;
  final int? linked;
  final SentEvalPause? pause;
  final List<SentEvalPhoneme>? phonemes;
  final List<SentEvalPhonic>? phonics;
  final SentEvalWordScores? scores;
  final SentEvalSpan? span;
  final String? word;
  final List<SentEvalWordPart>? word_parts;

  SentEvalWord({
    this.charType,
    this.linkable,
    this.linkable_type,
    this.linked,
    this.pause,
    this.phonemes,
    this.phonics,
    this.scores,
    this.span,
    this.word,
    this.word_parts,
  });
  factory SentEvalWord.fromJson(Map<String, dynamic> json) => _$SentEvalWordFromJson(json);
  Map<String, dynamic> toJson() => _$SentEvalWordToJson(this);
}

@JsonSerializable()
class SentEvalPause {
  final int? duration;
  final int? type;
  SentEvalPause({this.duration, this.type});
  factory SentEvalPause.fromJson(Map<String, dynamic> json) => _$SentEvalPauseFromJson(json);
  Map<String, dynamic> toJson() => _$SentEvalPauseToJson(this);
}

@JsonSerializable()
class SentEvalPhoneme {
  final String? phoneme;
  final int? pronunciation;
  final SentEvalSpan? span;
  final int? stress_mark;
  SentEvalPhoneme({this.phoneme, this.pronunciation, this.span, this.stress_mark});
  factory SentEvalPhoneme.fromJson(Map<String, dynamic> json) => _$SentEvalPhonemeFromJson(json);
  Map<String, dynamic> toJson() => _$SentEvalPhonemeToJson(this);
}

@JsonSerializable()
class SentEvalPhonic {
  final int? overall;
  final List<String>? phoneme;
  final String? spell;
  SentEvalPhonic({this.overall, this.phoneme, this.spell});
  factory SentEvalPhonic.fromJson(Map<String, dynamic> json) => _$SentEvalPhonicFromJson(json);
  Map<String, dynamic> toJson() => _$SentEvalPhonicToJson(this);
}

@JsonSerializable()
class SentEvalWordScores {
  final int? overall;
  final int? prominence;
  final int? pronunciation;
  SentEvalWordScores({this.overall, this.prominence, this.pronunciation});
  factory SentEvalWordScores.fromJson(Map<String, dynamic> json) => _$SentEvalWordScoresFromJson(json);
  Map<String, dynamic> toJson() => _$SentEvalWordScoresToJson(this);
}

@JsonSerializable()
class SentEvalSpan {
  final int? start;
  final int? end;
  SentEvalSpan({this.start, this.end});
  factory SentEvalSpan.fromJson(Map<String, dynamic> json) => _$SentEvalSpanFromJson(json);
  Map<String, dynamic> toJson() => _$SentEvalSpanToJson(this);
}

@JsonSerializable()
class SentEvalWordPart {
  final int? beginIndex;
  final int? charType;
  final int? endIndex;
  final String? part;
  SentEvalWordPart({this.beginIndex, this.charType, this.endIndex, this.part});
  factory SentEvalWordPart.fromJson(Map<String, dynamic> json) => _$SentEvalWordPartFromJson(json);
  Map<String, dynamic> toJson() => _$SentEvalWordPartToJson(this);
}

/// word.eval/word.eval.pro 单词评测结果
@JsonSerializable(explicitToJson: true)
class WordEvalResult {
  /// 总分
  final dynamic overall;
  /// 发音得分
  final dynamic pronunciation;
  /// 内核版本
  @JsonKey(name: 'kernel_version')
  final String? kernelVersion;
  /// 资源版本
  @JsonKey(name: 'resource_version')
  final String? resourceVersion;
  /// 单词详情（words）
  final List<WordEvalWord>? words;
  /// 音频时长（字符串，单位：秒）
  final String? duration;
  /// 数值型时长
  @JsonKey(name: 'numeric_duration')
  final dynamic numericDuration;
  /// 停顿次数
  @JsonKey(name: 'pause_count')
  final int? pauseCount;
  /// 重音分数
  final dynamic stress;
  /// 警告信息
  final List<SentEvalWarning>? warning;

  WordEvalResult({
    this.overall,
    this.pronunciation,
    this.kernelVersion,
    this.resourceVersion,
    this.words,
    this.duration,
    this.numericDuration,
    this.pauseCount,
    this.stress,
    this.warning,
  });

  factory WordEvalResult.fromJson(Map<String, dynamic> json) {
    List<WordEvalWord>? words;
    if (json['words'] != null) {
      words = (json['words'] as List)
          .map((e) => WordEvalWord.fromJson(e as Map<String, dynamic>))
          .toList();
    } else if (json['details'] != null) {
      words = (json['details'] as List)
          .map((e) => WordEvalWord.fromJson(e as Map<String, dynamic>))
          .toList();
    }
    List<SentEvalWarning>? warning;
    if (json['warning'] != null) {
      warning = (json['warning'] as List)
          .map((e) => SentEvalWarning.fromJson(e as Map<String, dynamic>))
          .toList();
    }
    return WordEvalResult(
      overall: json['overall'],
      pronunciation: json['pronunciation'],
      kernelVersion: json['kernel_version'],
      resourceVersion: json['resource_version'],
      words: words,
      duration: json['duration'],
      numericDuration: json['numeric_duration'],
      pauseCount: json['pause_count'],
      stress: json['stress'],
      warning: warning,
    );
  }
  Map<String, dynamic> toJson() => _$WordEvalResultToJson(this);
}

@JsonSerializable(explicitToJson: true)
class WordEvalWord {
  final String? word;
  final int? charType;
  final int? readType;
  final WordEvalPause? pause;
  final List<WordEvalPhoneme>? phonemes;
  final List<WordEvalPhonic>? phonics;
  final Map<String, dynamic>? scores;
  final WordEvalSpan? span;
  @JsonKey(name: 'word_parts')
  final List<WordEvalWordPart>? wordParts;

  WordEvalWord({
    this.word,
    this.charType,
    this.readType,
    this.pause,
    this.phonemes,
    this.phonics,
    this.scores,
    this.span,
    this.wordParts,
  });

  factory WordEvalWord.fromJson(Map<String, dynamic> json) => _$WordEvalWordFromJson(json);
  Map<String, dynamic> toJson() => _$WordEvalWordToJson(this);
}

@JsonSerializable()
class WordEvalPause {
  final int? duration;
  final int? type;
  WordEvalPause({this.duration, this.type});
  factory WordEvalPause.fromJson(Map<String, dynamic> json) => _$WordEvalPauseFromJson(json);
  Map<String, dynamic> toJson() => _$WordEvalPauseToJson(this);
}

@JsonSerializable()
class WordEvalPhoneme {
  final String? phoneme;
  final int? pronunciation;
  final WordEvalSpan? span;
  final int? stress_mark;
  WordEvalPhoneme({this.phoneme, this.pronunciation, this.span, this.stress_mark});
  factory WordEvalPhoneme.fromJson(Map<String, dynamic> json) => _$WordEvalPhonemeFromJson(json);
  Map<String, dynamic> toJson() => _$WordEvalPhonemeToJson(this);
}

@JsonSerializable()
class WordEvalPhonic {
  final int? overall;
  final List<String>? phoneme;
  final String? spell;
  WordEvalPhonic({this.overall, this.phoneme, this.spell});
  factory WordEvalPhonic.fromJson(Map<String, dynamic> json) => _$WordEvalPhonicFromJson(json);
  Map<String, dynamic> toJson() => _$WordEvalPhonicToJson(this);
}

@JsonSerializable()
class WordEvalSpan {
  final int? start;
  final int? end;
  WordEvalSpan({this.start, this.end});
  factory WordEvalSpan.fromJson(Map<String, dynamic> json) => _$WordEvalSpanFromJson(json);
  Map<String, dynamic> toJson() => _$WordEvalSpanToJson(this);
}

@JsonSerializable()
class WordEvalWordPart {
  final int? beginIndex;
  final int? charType;
  final int? endIndex;
  final String? part;
  WordEvalWordPart({this.beginIndex, this.charType, this.endIndex, this.part});
  factory WordEvalWordPart.fromJson(Map<String, dynamic> json) => _$WordEvalWordPartFromJson(json);
  Map<String, dynamic> toJson() => _$WordEvalWordPartToJson(this);
} 
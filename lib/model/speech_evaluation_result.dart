import 'package:json_annotation/json_annotation.dart';

part 'speech_evaluation_result.g.dart';

// 新增结构体的JsonSerializable生成声明
// ignore_for_file: non_constant_identifier_names

/// 语音评测通用响应体，result字段根据coreType类型动态解析
@JsonSerializable(explicitToJson: true)
class SpeechEvaluationResult {
  /// 终端用户请求id
  final String? tokenId;
  /// 评测文本
  final String? refText;
  /// 在线音频地址，需设置attachAudioUrl
  final String? audioUrl;
  /// 评分结果返回时间
  final String? dtLastResponse;
  /// 评分结果（类型为ParaEvalResult、SentEvalResult、WordEvalResult，需根据coreType判断）
  final dynamic result;
  /// appKey
  final String? applicationId;
  /// 评分唯一id，建议业务层保存，方便排查错误
  final String? recordId;
  /// 请求参数，需设置getParam
  final Map<String, dynamic>? params;
  /// 错误码，出现该字段时无result字段
  final int? errId;
  /// 错误码信息，出现该字段时无result字段
  final String? error;
  /// 0：中间结果；1：最终结果
  final int? eof;
  /// 中间结果时间戳（仅中间结果有此字段）
  final String? timestamp;

  SpeechEvaluationResult({
    this.tokenId,
    this.refText,
    this.audioUrl,
    this.dtLastResponse,
    this.result,
    this.applicationId,
    this.recordId,
    this.params,
    this.errId,
    this.error,
    this.eof,
    this.timestamp,
  });

  factory SpeechEvaluationResult.fromJson(Map<String, dynamic> json) {
    final coreType = json['params']?['request']?['coreType'] ?? '';
    dynamic resultObj;
    if (json['result'] != null) {
      if (coreType == 'sent.eval' || coreType == 'sent.eval.pro') {
        resultObj = SentEvalResult.fromJson(json['result']);
      } else if (coreType == 'para.eval') {
        resultObj = ParaEvalResult.fromJson(json['result']);
      } else if (coreType == 'word.eval' || coreType == 'word.eval.pro') {
        resultObj = WordEvalResult.fromJson(json['result']);
      } else {
        resultObj = json['result'];
      }
    }
    return SpeechEvaluationResult(
      tokenId: json['tokenId'],
      refText: json['refText'],
      audioUrl: json['audioUrl'],
      dtLastResponse: json['dtLastResponse'],
      result: resultObj,
      applicationId: json['applicationId'],
      recordId: json['recordId'],
      params: json['params'],
      errId: json['errId'],
      error: json['error'],
      eof: json['eof'],
      timestamp: json['timestamp'],
    );
  }
  Map<String, dynamic> toJson() => _$SpeechEvaluationResultToJson(this);
}

/// para.eval 段落评测结果
@JsonSerializable(explicitToJson: true)
class ParaEvalResult {
  /// 发音得分
  final dynamic pronunciation;
  /// 语速（词/分）
  final int? speed;
  /// 内核版本
  @JsonKey(name: 'kernel_version')
  final String? kernelVersion;
  /// 完整度
  final dynamic integrity;
  /// 资源版本
  @JsonKey(name: 'resource_version')
  final String? resourceVersion;
  /// 音频检测的提示
  final List<EvaluationWarning>? warning;
  /// 段落详情（句子数组）
  final List<ParaEvalSentence>? sentences;
  /// 韵律度得分
  final dynamic rhythm;
  /// 总分
  final dynamic overall;
  /// 流利度
  final dynamic fluency;
  /// 音频时长（字符串，单位：秒）
  final String? duration;
  /// 音频时长（数值，单位：秒）
  @JsonKey(name: 'numeric_duration')
  final dynamic numericDuration;

  ParaEvalResult({
    this.pronunciation,
    this.speed,
    this.kernelVersion,
    this.integrity,
    this.resourceVersion,
    this.warning,
    this.sentences,
    this.rhythm,
    this.overall,
    this.fluency,
    this.duration,
    this.numericDuration,
  });

  factory ParaEvalResult.fromJson(Map<String, dynamic> json) {
    List<EvaluationWarning>? warning;
    if (json['warning'] != null) {
      warning = (json['warning'] as List)
          .map((e) => EvaluationWarning.fromJson(e as Map<String, dynamic>))
          .toList();
    }

    List<ParaEvalSentence>? sentences;
    if (json['sentences'] != null) {
      sentences = (json['sentences'] as List)
          .map((e) => ParaEvalSentence.fromJson(e as Map<String, dynamic>))
          .toList();
    }

    return ParaEvalResult(
      pronunciation: json['pronunciation'],
      speed: json['speed'],
      kernelVersion: json['kernel_version'],
      integrity: json['integrity'],
      resourceVersion: json['resource_version'],
      warning: warning,
      sentences: sentences,
      rhythm: json['rhythm'],
      overall: json['overall'],
      fluency: json['fluency'],
      duration: json['duration'],
      numericDuration: json['numeric_duration'],
    );
  }

  Map<String, dynamic> toJson() => {
    'pronunciation': pronunciation,
    'speed': speed,
    'kernel_version': kernelVersion,
    'integrity': integrity,
    'resource_version': resourceVersion,
    'warning': warning?.map((e) => e.toJson()).toList(),
    'sentences': sentences?.map((e) => e.toJson()).toList(),
    'rhythm': rhythm,
    'overall': overall,
    'fluency': fluency,
    'duration': duration,
    'numeric_duration': numericDuration,
  };
}

/// 段落评测-句子详情
@JsonSerializable(explicitToJson: true)
class ParaEvalSentence {
  /// 句子内容
  final String? sentence;
  /// 句子总分
  final dynamic overall;
  /// 句子开始在音轨上的时间（单位：10毫秒）
  final int? start;
  /// 句子结束在音轨上的时间（单位：10毫秒）
  final int? end;
  /// 句子在文本中开始的位置
  final int? beginIndex;
  /// 句子在文本中结束的位置
  final int? endIndex;
  /// 句子中单词的详细信息（需开启paragraph_need_word_score）
  final List<ParaEvalWordDetail>? details;

  ParaEvalSentence({
    this.sentence,
    this.overall,
    this.start,
    this.end,
    this.beginIndex,
    this.endIndex,
    this.details,
  });

  factory ParaEvalSentence.fromJson(Map<String, dynamic> json) => _$ParaEvalSentenceFromJson(json);
  Map<String, dynamic> toJson() => _$ParaEvalSentenceToJson(this);
}

/// 段落评测-单词详情
@JsonSerializable(explicitToJson: true)
class ParaEvalWordDetail {
  /// 单词
  final String? word;
  /// 单词分数
  final dynamic overall;
  /// 单词重读，0=非重读，1=重读
  final int? prominence;
  /// 0=非标点，1=标点
  final int? charType;
  /// 0=正常，3=漏读，4=重复读
  final int? readType;
  /// 是否已读，0=已读，1=未读
  final int? skipped;
  /// 单词在音轨上的开始时间（单位：10毫秒）
  final int? start;
  /// 单词在音轨上的结束时间（单位：10毫秒）
  final int? end;
  /// 单词文本信息
  @JsonKey(name: 'word_parts')
  final List<EvaluationWordPart>? wordParts;
  /// 单词得分情况（仅readType=4时有）
  final Map<String, dynamic>? scores;

  ParaEvalWordDetail({
    this.word,
    this.overall,
    this.prominence,
    this.charType,
    this.readType,
    this.skipped,
    this.start,
    this.end,
    this.wordParts,
    this.scores,
  });

  factory ParaEvalWordDetail.fromJson(Map<String, dynamic> json) {
    List<EvaluationWordPart>? wordParts;
    if (json['word_parts'] != null) {
      wordParts = (json['word_parts'] as List)
          .map((e) => EvaluationWordPart.fromJson(e as Map<String, dynamic>))
          .toList();
    }

    return ParaEvalWordDetail(
      word: json['word'],
      overall: json['overall'],
      prominence: json['prominence'],
      charType: json['charType'],
      readType: json['readType'],
      skipped: json['skipped'],
      start: json['start'],
      end: json['end'],
      wordParts: wordParts,
      scores: json['scores'],
    );
  }

  Map<String, dynamic> toJson() => {
    'word': word,
    'overall': overall,
    'prominence': prominence,
    'charType': charType,
    'readType': readType,
    'skipped': skipped,
    'start': start,
    'end': end,
    'word_parts': wordParts?.map((e) => e.toJson()).toList(),
    'scores': scores,
  };
}



/// sent.eval/sent.eval.pro 句子评测结果
@JsonSerializable(explicitToJson: true)
class SentEvalResult {
  /// 评测时长（字符串）
  final String? duration;
  /// 流利度
  final dynamic fluency;
  /// 完整度
  final dynamic integrity;
  /// 内核版本
  @JsonKey(name: 'kernel_version')
  final String? kernelVersion;
  /// 数值型时长
  @JsonKey(name: 'numeric_duration')
  final dynamic numericDuration;
  /// 总分
  final dynamic overall;
  /// 停顿次数
  @JsonKey(name: 'pause_count')
  final int? pauseCount;
  /// 发音得分
  final dynamic pronunciation;
  /// 句尾语调
  @JsonKey(name: 'rear_tone')
  final String? rearTone;
  /// 资源版本
  @JsonKey(name: 'resource_version')
  final String? resourceVersion;
  /// 节奏/韵律度
  final dynamic rhythm;
  /// 语速
  final int? speed;
  /// 警告信息
  final List<EvaluationWarning>? warning;
  /// 单词详情
  final List<SentEvalWord>? words;
  /// 连读检测（支持连读且实际发连读音，则出现该维度）
  final List<SentEvalLiaison>? liaison;
  /// 不完全爆破检测（支持爆破且实际发爆破音，则出现该维度，且仅支持两个词之间的爆破）
  final List<SentEvalPlosion>? plosion;

  SentEvalResult({
    this.duration,
    this.fluency,
    this.integrity,
    this.kernelVersion,
    this.numericDuration,
    this.overall,
    this.pauseCount,
    this.pronunciation,
    this.rearTone,
    this.resourceVersion,
    this.rhythm,
    this.speed,
    this.warning,
    this.words,
    this.liaison,
    this.plosion,
  });

  factory SentEvalResult.fromJson(Map<String, dynamic> json) {
    List<EvaluationWarning>? warning;
    if (json['warning'] != null) {
      warning = (json['warning'] as List)
          .map((e) => EvaluationWarning.fromJson(e as Map<String, dynamic>))
          .toList();
    }

    List<SentEvalWord>? words;
    if (json['words'] != null) {
      words = (json['words'] as List)
          .map((e) => SentEvalWord.fromJson(e as Map<String, dynamic>))
          .toList();
    }

    List<SentEvalLiaison>? liaison;
    if (json['liaison'] != null) {
      liaison = (json['liaison'] as List)
          .map((e) => SentEvalLiaison.fromJson(e as Map<String, dynamic>))
          .toList();
    }

    List<SentEvalPlosion>? plosion;
    if (json['plosion'] != null) {
      plosion = (json['plosion'] as List)
          .map((e) => SentEvalPlosion.fromJson(e as Map<String, dynamic>))
          .toList();
    }

    return SentEvalResult(
      duration: json['duration'],
      fluency: json['fluency'],
      integrity: json['integrity'],
      kernelVersion: json['kernel_version'],
      numericDuration: json['numeric_duration'],
      overall: json['overall'],
      pauseCount: json['pause_count'],
      pronunciation: json['pronunciation'],
      rearTone: json['rear_tone'],
      resourceVersion: json['resource_version'],
      rhythm: json['rhythm'],
      speed: json['speed'],
      warning: warning,
      words: words,
      liaison: liaison,
      plosion: plosion,
    );
  }

  Map<String, dynamic> toJson() => {
    'duration': duration,
    'fluency': fluency,
    'integrity': integrity,
    'kernel_version': kernelVersion,
    'numeric_duration': numericDuration,
    'overall': overall,
    'pause_count': pauseCount,
    'pronunciation': pronunciation,
    'rear_tone': rearTone,
    'resource_version': resourceVersion,
    'rhythm': rhythm,
    'speed': speed,
    'warning': warning?.map((e) => e.toJson()).toList(),
    'words': words?.map((e) => e.toJson()).toList(),
    'liaison': liaison?.map((e) => e.toJson()).toList(),
    'plosion': plosion?.map((e) => e.toJson()).toList(),
  };
}



@JsonSerializable(explicitToJson: true)
class SentEvalWord {
  final int? charType;
  final int? linkable;
  final int? linkable_type;
  final int? linked;
  final SentEvalPause? pause;
  final List<SentEvalPhoneme>? phonemes;
  final List<SentEvalPhonic>? phonics;
  final SentEvalWordScores? scores;
  final SentEvalSpan? span;
  final String? word;
  final List<SentEvalWordPart>? word_parts;

  SentEvalWord({
    this.charType,
    this.linkable,
    this.linkable_type,
    this.linked,
    this.pause,
    this.phonemes,
    this.phonics,
    this.scores,
    this.span,
    this.word,
    this.word_parts,
  });
  factory SentEvalWord.fromJson(Map<String, dynamic> json) => _$SentEvalWordFromJson(json);
  Map<String, dynamic> toJson() => _$SentEvalWordToJson(this);
}

@JsonSerializable()
class SentEvalPause {
  final int? duration;
  final int? type;
  SentEvalPause({this.duration, this.type});
  factory SentEvalPause.fromJson(Map<String, dynamic> json) => _$SentEvalPauseFromJson(json);
  Map<String, dynamic> toJson() => _$SentEvalPauseToJson(this);
}

@JsonSerializable()
class SentEvalPhoneme {
  final String? phoneme;
  final int? pronunciation;
  final SentEvalSpan? span;
  final int? stress_mark;
  SentEvalPhoneme({this.phoneme, this.pronunciation, this.span, this.stress_mark});
  factory SentEvalPhoneme.fromJson(Map<String, dynamic> json) => _$SentEvalPhonemeFromJson(json);
  Map<String, dynamic> toJson() => _$SentEvalPhonemeToJson(this);
}

@JsonSerializable()
class SentEvalPhonic {
  final int? overall;
  final List<String>? phoneme;
  final String? spell;
  SentEvalPhonic({this.overall, this.phoneme, this.spell});
  factory SentEvalPhonic.fromJson(Map<String, dynamic> json) => _$SentEvalPhonicFromJson(json);
  Map<String, dynamic> toJson() => _$SentEvalPhonicToJson(this);
}

@JsonSerializable()
class SentEvalWordScores {
  final int? overall;
  final int? prominence;
  final int? pronunciation;
  SentEvalWordScores({this.overall, this.prominence, this.pronunciation});
  factory SentEvalWordScores.fromJson(Map<String, dynamic> json) => _$SentEvalWordScoresFromJson(json);
  Map<String, dynamic> toJson() => _$SentEvalWordScoresToJson(this);
}

@JsonSerializable()
class SentEvalSpan {
  final int? start;
  final int? end;
  SentEvalSpan({this.start, this.end});
  factory SentEvalSpan.fromJson(Map<String, dynamic> json) => _$SentEvalSpanFromJson(json);
  Map<String, dynamic> toJson() => _$SentEvalSpanToJson(this);
}

@JsonSerializable()
class SentEvalWordPart {
  final int? beginIndex;
  final int? charType;
  final int? endIndex;
  final String? part;
  SentEvalWordPart({this.beginIndex, this.charType, this.endIndex, this.part});
  factory SentEvalWordPart.fromJson(Map<String, dynamic> json) => _$SentEvalWordPartFromJson(json);
  Map<String, dynamic> toJson() => _$SentEvalWordPartToJson(this);
}

/// word.eval/word.eval.pro 单词评测结果
@JsonSerializable(explicitToJson: true)
class WordEvalResult {
  /// 总分
  final dynamic overall;
  /// 发音得分
  final dynamic pronunciation;
  /// 内核版本
  @JsonKey(name: 'kernel_version')
  final String? kernelVersion;
  /// 资源版本
  @JsonKey(name: 'resource_version')
  final String? resourceVersion;
  /// 单词详情（words）
  final List<WordEvalWord>? words;
  /// 音频时长（字符串，单位：秒）
  final String? duration;
  /// 数值型时长
  @JsonKey(name: 'numeric_duration')
  final dynamic numericDuration;
  /// 停顿次数
  @JsonKey(name: 'pause_count')
  final int? pauseCount;
  /// 重音分数
  final dynamic stress;
  /// 警告信息
  final List<EvaluationWarning>? warning;
  /// 韵律度得分
  final dynamic rhythm;
  /// 流利度
  final dynamic fluency;
  /// 完整度
  final dynamic integrity;
  /// 语速
  final int? speed;

  WordEvalResult({
    this.overall,
    this.pronunciation,
    this.kernelVersion,
    this.resourceVersion,
    this.words,
    this.duration,
    this.numericDuration,
    this.pauseCount,
    this.stress,
    this.warning,
    this.rhythm,
    this.fluency,
    this.integrity,
    this.speed,
  });

  factory WordEvalResult.fromJson(Map<String, dynamic> json) {
    List<WordEvalWord>? words;
    if (json['words'] != null) {
      words = (json['words'] as List)
          .map((e) => WordEvalWord.fromJson(e as Map<String, dynamic>))
          .toList();
    } else if (json['details'] != null) {
      words = (json['details'] as List)
          .map((e) => WordEvalWord.fromJson(e as Map<String, dynamic>))
          .toList();
    }
    List<EvaluationWarning>? warning;
    if (json['warning'] != null) {
      warning = (json['warning'] as List)
          .map((e) => EvaluationWarning.fromJson(e as Map<String, dynamic>))
          .toList();
    }
    return WordEvalResult(
      overall: json['overall'],
      pronunciation: json['pronunciation'],
      kernelVersion: json['kernel_version'],
      resourceVersion: json['resource_version'],
      words: words,
      duration: json['duration'],
      numericDuration: json['numeric_duration'],
      pauseCount: json['pause_count'],
      stress: json['stress'],
      warning: warning,
      rhythm: json['rhythm'],
      fluency: json['fluency'],
      integrity: json['integrity'],
      speed: json['speed'],
    );
  }
  Map<String, dynamic> toJson() => {
    'overall': overall,
    'pronunciation': pronunciation,
    'kernel_version': kernelVersion,
    'resource_version': resourceVersion,
    'words': words?.map((e) => e.toJson()).toList(),
    'duration': duration,
    'numeric_duration': numericDuration,
    'pause_count': pauseCount,
    'stress': stress,
    'warning': warning?.map((e) => e.toJson()).toList(),
    'rhythm': rhythm,
    'fluency': fluency,
    'integrity': integrity,
    'speed': speed,
  };
}

@JsonSerializable(explicitToJson: true)
class WordEvalWord {
  final String? word;
  final int? charType;
  final int? readType;
  final EvaluationPause? pause;
  final List<WordEvalPhoneme>? phonemes;
  final List<WordEvalPhonic>? phonics;
  final Map<String, dynamic>? scores;
  final EvaluationSpan? span;
  @JsonKey(name: 'word_parts')
  final List<EvaluationWordPart>? wordParts;

  WordEvalWord({
    this.word,
    this.charType,
    this.readType,
    this.pause,
    this.phonemes,
    this.phonics,
    this.scores,
    this.span,
    this.wordParts,
  });

  factory WordEvalWord.fromJson(Map<String, dynamic> json) {
    EvaluationPause? pause;
    if (json['pause'] != null) {
      pause = EvaluationPause.fromJson(json['pause']);
    }

    EvaluationSpan? span;
    if (json['span'] != null) {
      span = EvaluationSpan.fromJson(json['span']);
    }

    List<EvaluationWordPart>? wordParts;
    if (json['word_parts'] != null) {
      wordParts = (json['word_parts'] as List)
          .map((e) => EvaluationWordPart.fromJson(e as Map<String, dynamic>))
          .toList();
    }

    List<WordEvalPhoneme>? phonemes;
    if (json['phonemes'] != null) {
      phonemes = (json['phonemes'] as List)
          .map((e) => WordEvalPhoneme.fromJson(e as Map<String, dynamic>))
          .toList();
    }

    List<WordEvalPhonic>? phonics;
    if (json['phonics'] != null) {
      phonics = (json['phonics'] as List)
          .map((e) => WordEvalPhonic.fromJson(e as Map<String, dynamic>))
          .toList();
    }

    return WordEvalWord(
      word: json['word'],
      charType: json['charType'],
      readType: json['readType'],
      pause: pause,
      phonemes: phonemes,
      phonics: phonics,
      scores: json['scores'],
      span: span,
      wordParts: wordParts,
    );
  }

  Map<String, dynamic> toJson() => {
    'word': word,
    'charType': charType,
    'readType': readType,
    'pause': pause?.toJson(),
    'phonemes': phonemes?.map((e) => e.toJson()).toList(),
    'phonics': phonics?.map((e) => e.toJson()).toList(),
    'scores': scores,
    'span': span?.toJson(),
    'word_parts': wordParts?.map((e) => e.toJson()).toList(),
  };
}



@JsonSerializable()
class WordEvalPhoneme {
  final String? phoneme;
  final int? pronunciation;
  final EvaluationSpan? span;
  final int? stress_mark;

  WordEvalPhoneme({this.phoneme, this.pronunciation, this.span, this.stress_mark});

  factory WordEvalPhoneme.fromJson(Map<String, dynamic> json) => WordEvalPhoneme(
    phoneme: json['phoneme'],
    pronunciation: json['pronunciation'],
    span: json['span'] != null ? EvaluationSpan.fromJson(json['span']) : null,
    stress_mark: json['stress_mark'],
  );

  Map<String, dynamic> toJson() => {
    'phoneme': phoneme,
    'pronunciation': pronunciation,
    'span': span?.toJson(),
    'stress_mark': stress_mark,
  };
}

@JsonSerializable()
class WordEvalPhonic {
  final int? overall;
  final List<String>? phoneme;
  final String? spell;
  WordEvalPhonic({this.overall, this.phoneme, this.spell});
  factory WordEvalPhonic.fromJson(Map<String, dynamic> json) => _$WordEvalPhonicFromJson(json);
  Map<String, dynamic> toJson() => _$WordEvalPhonicToJson(this);
}





/// 通用的评测警告信息类（可复用于所有评测类型）
@JsonSerializable()
class EvaluationWarning {
  /// 错误码
  final int? code;
  /// 音频检测的提示信息
  final String? message;

  EvaluationWarning({this.code, this.message});

  factory EvaluationWarning.fromJson(Map<String, dynamic> json) => EvaluationWarning(
    code: json['code'],
    message: json['message'],
  );

  Map<String, dynamic> toJson() => {
    'code': code,
    'message': message,
  };
}

/// 通用的时间跨度类（可复用于所有评测类型）
@JsonSerializable()
class EvaluationSpan {
  /// 开始时间（单位：10毫秒）
  final int? start;
  /// 结束时间（单位：10毫秒）
  final int? end;

  EvaluationSpan({this.start, this.end});

  factory EvaluationSpan.fromJson(Map<String, dynamic> json) => EvaluationSpan(
    start: json['start'],
    end: json['end'],
  );

  Map<String, dynamic> toJson() => {
    'start': start,
    'end': end,
  };
}

/// 通用的单词文本信息类（可复用于所有评测类型）
@JsonSerializable()
class EvaluationWordPart {
  /// 文本内容
  final String? part;
  /// 在文本中开始的位置
  final int? beginIndex;
  /// 在文本中结束的位置
  final int? endIndex;
  /// 0=非标点，1=标点
  final int? charType;

  EvaluationWordPart({this.part, this.beginIndex, this.endIndex, this.charType});

  factory EvaluationWordPart.fromJson(Map<String, dynamic> json) => EvaluationWordPart(
    part: json['part'],
    beginIndex: json['beginIndex'],
    endIndex: json['endIndex'],
    charType: json['charType'],
  );

  Map<String, dynamic> toJson() => {
    'part': part,
    'beginIndex': beginIndex,
    'endIndex': endIndex,
    'charType': charType,
  };
}

/// 通用的停顿信息类（可复用于所有评测类型）
@JsonSerializable()
class EvaluationPause {
  /// 停顿时长（单位：10毫秒）
  final int? duration;
  /// 停顿类型，duration>10该值为1，否则值为0
  final int? type;

  EvaluationPause({this.duration, this.type});

  factory EvaluationPause.fromJson(Map<String, dynamic> json) => EvaluationPause(
    duration: json['duration'],
    type: json['type'],
  );

  Map<String, dynamic> toJson() => {
    'duration': duration,
    'type': type,
  };
}

/// 连读检测信息
@JsonSerializable(explicitToJson: true)
class SentEvalLiaison {
  /// 连读的第一个单词信息
  final SentEvalLiaisonWord? first;
  /// 连读的第二个单词信息
  final SentEvalLiaisonWord? second;
  /// 第一个单词的最后一个音素
  @JsonKey(name: 'first_phoneme')
  final String? firstPhoneme;
  /// 第二个单词的第一个音素
  @JsonKey(name: 'second_phoneme')
  final String? secondPhoneme;
  /// 连读类型
  @JsonKey(name: 'linkable_type')
  final int? linkableType;

  SentEvalLiaison({
    this.first,
    this.second,
    this.firstPhoneme,
    this.secondPhoneme,
    this.linkableType,
  });

  factory SentEvalLiaison.fromJson(Map<String, dynamic> json) => SentEvalLiaison(
    first: json['first'] != null ? SentEvalLiaisonWord.fromJson(json['first']) : null,
    second: json['second'] != null ? SentEvalLiaisonWord.fromJson(json['second']) : null,
    firstPhoneme: json['first_phoneme'],
    secondPhoneme: json['second_phoneme'],
    linkableType: json['linkable_type'],
  );

  Map<String, dynamic> toJson() => {
    'first': first?.toJson(),
    'second': second?.toJson(),
    'first_phoneme': firstPhoneme,
    'second_phoneme': secondPhoneme,
    'linkable_type': linkableType,
  };
}

/// 连读单词信息
@JsonSerializable()
class SentEvalLiaisonWord {
  /// 单词位置信息
  final int? index;
  /// 单词文本
  final String? word;

  SentEvalLiaisonWord({this.index, this.word});

  factory SentEvalLiaisonWord.fromJson(Map<String, dynamic> json) => SentEvalLiaisonWord(
    index: json['index'],
    word: json['word'],
  );

  Map<String, dynamic> toJson() => {
    'index': index,
    'word': word,
  };
}

/// 不完全爆破检测信息
@JsonSerializable(explicitToJson: true)
class SentEvalPlosion {
  /// 爆破音的第一个单词信息
  final SentEvalLiaisonWord? first;
  /// 爆破音的第二个单词信息
  final SentEvalLiaisonWord? second;
  /// 第一个单词的最后一个音素
  @JsonKey(name: 'first_phoneme')
  final String? firstPhoneme;
  /// 第二个单词的第一个音素
  @JsonKey(name: 'second_phoneme')
  final String? secondPhoneme;
  /// 连读类型
  @JsonKey(name: 'linkable_type')
  final int? linkableType;

  SentEvalPlosion({
    this.first,
    this.second,
    this.firstPhoneme,
    this.secondPhoneme,
    this.linkableType,
  });

  factory SentEvalPlosion.fromJson(Map<String, dynamic> json) => SentEvalPlosion(
    first: json['first'] != null ? SentEvalLiaisonWord.fromJson(json['first']) : null,
    second: json['second'] != null ? SentEvalLiaisonWord.fromJson(json['second']) : null,
    firstPhoneme: json['first_phoneme'],
    secondPhoneme: json['second_phoneme'],
    linkableType: json['linkable_type'],
  );

  Map<String, dynamic> toJson() => {
    'first': first?.toJson(),
    'second': second?.toJson(),
    'first_phoneme': firstPhoneme,
    'second_phoneme': secondPhoneme,
    'linkable_type': linkableType,
  };
}
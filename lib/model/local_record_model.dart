import 'package:json_annotation/json_annotation.dart';

part 'local_record_model.g.dart';

//本地存储录音
@JsonSerializable()
class LocalRecordModel {
  String? localFilePath;
  String? localRecordPath;
  int? subtitleIndex;
  int? score;
  String? userString;

  LocalRecordModel({
    this.localFilePath,
    this.localRecordPath,
    this.subtitleIndex,
    this.score,
    this.userString,
  });

  factory LocalRecordModel.fromJson(Map<String, dynamic> json) {
    return _$LocalRecordModelFromJson(json);
  }

  Map<String, dynamic> toJson() => _$LocalRecordModelToJson(this);
}

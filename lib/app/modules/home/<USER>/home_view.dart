import 'package:circular_gradient_progress/circular_gradient_progress.dart';
import 'package:flutter/material.dart';
import 'package:focus_detector/focus_detector.dart';
import 'package:gap/gap.dart';

import 'package:get/get.dart';
import 'package:lsenglish/app/routes/app_pages.dart';
import 'package:lsenglish/base/loading_getview.dart';
import 'package:lsenglish/r.dart';
import 'package:lsenglish/widgets/default_widget.dart';
import 'package:lsenglish/utils/image.dart';
import 'package:lsenglish/utils/size_extension.dart';
import '../controllers/home_controller.dart';

class HomeView extends LoadingGetView<HomeController> {
  const HomeView({Key? key}) : super(key: key);
  @override
  Widget buildSuccess(BuildContext context) {
    return FocusDetector(
      onFocusGained: () => controller.onFocusGained(),
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            '学习',
            style: TextStyle(fontSize: 24.whs, fontWeight: FontWeight.w600),
          ),
          centerTitle: false,
          actions: [
            GestureDetector(
              onTap: () => controller.importVideo(context: context),
              child: Padding(
                padding: EdgeInsets.only(right: 16.whs),
                child: Icon(
                  Icons.add,
                  size: 30.whs,
                ),
              ),
            ),
            GestureDetector(
              onTap: () => Get.toNamed(Routes.RESOURCELIB),
              child: Padding(
                padding: EdgeInsets.only(right: 16.whs),
                child: ImageLoader(R.book, size: 24.whs),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(right: 16.whs),
              child: Row(
                children: [
                  Text(
                    "目标",
                    style: Get.textTheme.bodyLarge,
                  ),
                  Gap(4.whs),
                  ImageLoader(R.edit, size: 18.whs, color: Get.theme.colorScheme.onSecondary)
                ],
              ),
            ),
            // GestureDetector(
            //   onTap: () => Get.to(() => const FileManager(), preventDuplicates: false),
            //   child: Padding(
            //     padding: EdgeInsets.all(8.whs),
            //     child: Icon(
            //       Icons.file_open_rounded,
            //       size: 30.whs,
            //     ),
            //   ),
            // ),
          ],
        ),
        body: SingleChildScrollView(
          child: Column(
            children: [
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.whs),
                child: Container(
                  padding: EdgeInsets.all(12.whs),
                  clipBehavior: Clip.antiAlias,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8.whs),
                    color: Get.theme.primaryColor,
                  ),
                  child: Row(
                    children: [
                      ImageLoader(R.logo, size: 40.whs),
                      Gap(4.whs),
                      Expanded(
                        child: Text(
                          'LS 训练法介绍及使用方法',
                          style: TextStyle(color: Colors.white, fontSize: 18.whs, fontWeight: FontWeight.w600),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Obx(
                () => controller.watchHistorys.isEmpty ? _buildEmptyHistoryWidget(context) : _buildHistoryListWidget(),
              ),
              Gap(32.whs),
              _buildCollectWidget(),
              Gap(32.whs),
              _buildNoteWidget(),
              Gap(32.whs),
              _buildDemoWidget(),
              Gap(32.whs),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHistoryListWidget() {
    return Column(
      children: [
        Gap(16.whs),
        SizedBox(
          height: 270,
          child: Obx(() => ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: controller.watchHistorys.length,
                itemBuilder: (BuildContext context, int index) {
                  return GestureDetector(
                    onTap: () {
                      controller.historyClick(index);
                    },
                    onLongPress: () => controller.historyLongPress(index),
                    child: Padding(
                      padding: EdgeInsets.only(left: index == 0 ? 16.whs : 8.whs, right: 8.whs),
                      child: SizedBox(
                        width: 320,
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Container(
                              clipBehavior: Clip.antiAlias,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(16.whs),
                                color: const Color(0xffEFEFF0),
                              ),
                              child: AspectRatio(
                                aspectRatio: 393 / 220,
                                child: Stack(
                                  children: [
                                    Positioned.fill(child: ImageLoader(controller.watchHistorys[index].cover, size: 160.whs)),
                                    Center(child: ImageLoader(R.play, color: Colors.white, size: 45.whs))
                                  ],
                                ),
                              ),
                            ),
                            Gap(16.whs),
                            Row(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Expanded(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(controller.watchHistorys[index].name,
                                          overflow: TextOverflow.ellipsis, style: TextStyle(fontSize: 19.whs, fontWeight: FontWeight.w600)),
                                      Gap(4.whs),
                                      Text(
                                        "1/100-LS 12 min",
                                        style: TextStyle(fontSize: 16.whs, color: const Color(0xFF625B71), fontWeight: FontWeight.w400),
                                      ),
                                    ],
                                  ),
                                ),
                                CircularGradientProgressWidget(
                                  size: 32.whs,
                                  sweepAngle: 180,
                                  gradientColors: [Get.theme.primaryColor, Get.theme.primaryColor],
                                  backgroundColor: Get.theme.primaryColor.withOpacity(0.22),
                                  strokeWidth: 6.whs,
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              )),
        ),
      ],
    );
  }

  Widget _buildCollectWidget() {
    return Column(
      children: [
        Padding(
            padding: EdgeInsets.only(left: 16.whs, right: 16.whs),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text("收藏", style: TextStyle(fontSize: 24.whs, fontWeight: FontWeight.w600)),
                Row(
                  children: [
                    Text("View All", style: TextStyle(fontSize: 16.whs, fontWeight: FontWeight.w600, color: const Color(0xff0055FF))),
                    Icon(Icons.arrow_forward_ios_rounded, size: 16.whs, color: const Color(0xff0055FF))
                  ],
                )
              ],
            )),
        Gap(16.whs),
        SizedBox(
          height: 235.whs,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: 5,
            itemBuilder: (BuildContext context, int index) {
              return Padding(
                padding: EdgeInsets.only(left: index == 0 ? 16.whs : 8.whs, right: 8.whs),
                child: AspectRatio(
                  aspectRatio: 0.7,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                          clipBehavior: Clip.antiAlias,
                          decoration: BoxDecoration(borderRadius: BorderRadius.circular(16.whs)),
                          child: AspectRatio(
                            aspectRatio: 1,
                            child: ImageLoader("https://img0.baidu.com/it/u=805205735,4277764239&fm=253&fmt=auto&app=138&f=PNG?w=854&h=480",
                                size: 160.whs),
                          )),
                      Gap(4.whs),
                      Text('Belly fat burner HIIT', style: TextStyle(color: Color(0xFF1D192B), fontSize: 16.whs, fontWeight: FontWeight.w600)),
                      Gap(4.whs),
                      Text('12 min ', style: TextStyle(color: Color(0xFF888888), fontSize: 14.whs)),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildNoteWidget() {
    return Column(
      children: [
        Padding(
            padding: EdgeInsets.only(left: 16.whs, right: 16.whs),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text("笔记", style: TextStyle(fontSize: 24.whs, fontWeight: FontWeight.w600)),
                Row(
                  children: [
                    Text("View All", style: TextStyle(fontSize: 16.whs, fontWeight: FontWeight.w600, color: const Color(0xff0055FF))),
                    Icon(Icons.arrow_forward_ios_rounded, size: 16.whs, color: const Color(0xff0055FF))
                  ],
                )
              ],
            )),
        Gap(16.whs),
        SizedBox(
          height: 235.whs,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: 5,
            itemBuilder: (BuildContext context, int index) {
              return Padding(
                padding: EdgeInsets.only(left: index == 0 ? 16.whs : 8.whs, right: 8.whs),
                child: AspectRatio(
                  aspectRatio: 0.7,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                          clipBehavior: Clip.antiAlias,
                          decoration: BoxDecoration(borderRadius: BorderRadius.circular(16.whs)),
                          child: AspectRatio(
                            aspectRatio: 1,
                            child: ImageLoader("https://img0.baidu.com/it/u=805205735,4277764239&fm=253&fmt=auto&app=138&f=PNG?w=854&h=480",
                                size: 160.whs),
                          )),
                      Gap(4.whs),
                      Text('Belly fat burner HIIT', style: TextStyle(color: Color(0xFF1D192B), fontSize: 16.whs, fontWeight: FontWeight.w600)),
                      Gap(4.whs),
                      Text('12 min ', style: TextStyle(color: Color(0xFF888888), fontSize: 14.whs)),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyHistoryWidget(BuildContext context) {
    return Column(
      children: [
        NodataWidget(
          message: "您没有学习记录哦",
          imageSize: 130.whs,
        ),
        Gap(32.whs),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            GestureDetector(
              onTap: () {
                Get.toNamed(Routes.RESOURCELIB);
              },
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 16.whs, vertical: 8.whs),
                clipBehavior: Clip.antiAlias,
                decoration: BoxDecoration(
                  color: const Color(0xff101828),
                  borderRadius: BorderRadius.circular(16.whs),
                ),
                child: Text(
                  '资源库',
                  textAlign: TextAlign.center,
                  style: TextStyle(color: const Color(0xFFFCFCFD), fontSize: 18.whs, fontWeight: FontWeight.w600),
                ),
              ),
            ),
            Gap(16.whs),
            GestureDetector(
              onTap: () {
                controller.importVideo(context: context);
              },
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 16.whs, vertical: 8.whs),
                clipBehavior: Clip.antiAlias,
                decoration: BoxDecoration(
                  color: const Color(0xffF2F4F7),
                  borderRadius: BorderRadius.circular(16.whs),
                ),
                child: Text(
                  '去导入',
                  textAlign: TextAlign.center,
                  style: TextStyle(color: const Color(0xFF101828), fontSize: 18.whs, fontWeight: FontWeight.w600),
                ),
              ),
            ),
          ],
        )
      ],
    );
  }

  Widget _buildDemoWidget() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(padding: EdgeInsets.only(left: 16.whs), child: Text("演示", style: TextStyle(fontSize: 24.whs, fontWeight: FontWeight.w600))),
        Gap(16.whs),
        SizedBox(
          height: 163.whs,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: 5,
            itemBuilder: (BuildContext context, int index) {
              return Padding(
                padding: EdgeInsets.only(left: index == 0 ? 16.whs : 8.whs, right: 8.whs),
                child: AspectRatio(
                  aspectRatio: 2,
                  child: Stack(
                    children: [
                      Positioned.fill(
                          child: Container(
                              clipBehavior: Clip.antiAlias,
                              decoration: BoxDecoration(borderRadius: BorderRadius.circular(16.whs)),
                              child: ImageLoader("https://img0.baidu.com/it/u=504153344,1603339128&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500")))
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}

import 'package:get/get.dart';
import 'package:lsenglish/app/modules/home/<USER>/home_controller.dart';
import 'package:lsenglish/app/modules/mine/controllers/mine_controller.dart';
import 'package:lsenglish/app/modules/notelist/controllers/notelist_controller.dart';
import 'package:lsenglish/app/modules/resourcelib/controllers/resourcelib_controller.dart';

import '../../datacenter/controllers/datacenter_controller.dart';
import '../controllers/main_controller.dart';

class MainBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<MainController>(
      () => MainController(),
    );
    Get.lazyPut<DatacenterController>(
      () => DatacenterController(),
    );
    Get.lazyPut<HomeController>(
      () => HomeController(),
    );
    Get.lazyPut<MineController>(
      () => MineController(),
    );
    Get.lazyPut<NoteListController>(
      () => NoteListController(),
    );
    Get.lazyPut<ResourcelibController>(
      () => ResourcelibController(),
    );
  }
}

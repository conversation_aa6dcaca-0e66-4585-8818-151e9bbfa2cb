import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:back_button_interceptor/back_button_interceptor.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart' as fcm;
import 'package:flutter_sound/flutter_sound.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:lsenglish/app/routes/app_pages.dart';
import 'package:lsenglish/base/base_controller.dart';
import 'package:lsenglish/config/config.dart';
import 'package:lsenglish/model/video_time_interval.dart';
import 'package:lsenglish/utils/ffmpeg.dart';
import 'package:lsenglish/utils/log.dart';
import 'package:lsenglish/utils/obs.dart';
import 'package:lsenglish/model/note_model.dart';
import 'package:lsenglish/net/net.dart';
import 'package:lsenglish/model/local_record_model.dart';
import 'package:lsenglish/utils/datacenter_time_manager.dart';
import 'package:lsenglish/utils/extension.dart';
import 'package:lsenglish/utils/file.dart';
import 'package:lsenglish/utils/login.dart';
import 'package:lsenglish/utils/player_menu.dart';
import 'package:lsenglish/utils/sound.dart';
import 'package:lsenglish/utils/sp.dart';
import 'package:lsenglish/utils/subtitle.dart';
import 'package:lsenglish/utils/toast.dart';
import 'package:lsenglish/utils/video.dart';
import 'package:lsenglish/utils/video_compression.dart';
import 'package:lsenglish/utils/xfyun.dart';
import 'package:lsenglish/video/media_player.dart';
import 'package:lsenglish/widgets/base_dialog.dart';
import 'package:lsenglish/widgets/ifly_speech_recognition/src/speech_recognition_service.dart';
import 'package:media_kit_video/media_kit_video.dart';
import 'package:path/path.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:scroll_to_index/scroll_to_index.dart';

import '../../../../model/local_detail_resp/resource_detail_resp.dart';
import '../../../../video/video_controls_material.dart';
import '../../../../widgets/lstimes_mod_widget.dart';
import '../views/add_note_widget.dart';
import '../../../../utils/ls_word_util.dart';
import '../views/more.dart';
import 'detail_util.dart';

class DetailController extends BaseStreamController with GetTickerProviderStateMixin, WidgetsBindingObserver {
  final SpeechRecognitionService _recognitionService = SpeechRecognitionService(
    appId: '1b89f1dc',
    appKey: '143ab331e3af3c0835ef1d07b9512a9f',
    appSecret: 'ODBmMzA1YTVlNzNhMjdmMGY4MWU2YTll',
  );
  var isLocalVideo = false;
  var remoteResourceId = "";
  FlutterSoundPlayer recordSoundPlayer = FlutterSoundPlayer(logLevel: Level.fatal);
  //进入页面的时候优先判断，免得每次在录制的判断，影响效率
  var hasMicPermission = false;
  var videoKit = MediaPlayer();
  var isLandscape = false.obs;
  //是否为用户手动跳转
  var pageChangeByUser = false.obs;
  late var videoController = VideoController(videoKit.getPlayer);
  final AutoScrollController itemScrollController = AutoScrollController(axis: Axis.vertical);
  late PageController pageController;
  bool _allowAutoScroll = true;
  Timer? _autoScrollTimer;
  var recordingInLsMode = false.obs;
  //录音后识别的文字
  var recordText = "".obs;
  var _videoUrlOrPath = "";
  var videoName = "".obs;
  var _recordPath = "";
  var currentHasRecordFile = false.obs;
  var recordIndexMap = <int, LocalRecordModel>{}.obs;
  ResourceDetailResp? localDetailResp;
  var showSubtitlePlaceholder = true.obs;
  var notesMap = <int, NoteModel>{}.obs;
  var sentenceCollectMap = <int, VideoTimeInterval?>{}.obs;
  var currentPage = 0.obs;
  var loadingSubtitle = true.obs;
  var currentToast = "".obs;
  var showCurrentLandScapeToast = false.obs;
  late var videoControlsCallbacks = VideoControlsCallbacks(
    onToggleFullscreen: () => videoKit.toggleFullscreen(),
    onDoubleTapPlayOrPause: () => videoKit.resetLsModeIndex(needPlay: false),
    onPlayOrPause: () => videoKit.resetLsModeIndex(),
    onPointerUp: () => videoKit.resetLsModeIndex(needPlay: false),
    onVideoSeekChangeEnd: () => videoKit.onVideoSeekChangeEnd(),
    onTap: () => videoKit.toggleFullscreen(),
  );
  var playerMenuItems = <PlayerMenu>[].obs;
  var totalDuration = 0.obs;
  var seekBarRatio = 0.0.obs;
  var canChangeSeekBarRatio = true;
  var isAppBarVisible = true.obs;

  @override
  void onInit() async {
    super.onInit();
    WidgetsBinding.instance.addObserver(this);
    BackButtonInterceptor.add(_interceptorPop);
    itemScrollController.addListener(_scrollListener);
    playerMenuItems.value = PlayerMenuManager().menuByConfig();
    pageController = PageController(keepPage: false);
    videoKit.init();
    videoKit.reset();
    remoteResourceId = Get.arguments['resourceId'] ?? "";
    _videoUrlOrPath = Get.arguments['videoUrlOrPath'] ?? "";
    videoName.value = Get.arguments['videoName'] ?? "";
    isLocalVideo = remoteResourceId.isEmpty;
    logger(
        "DetailController init remoteResourceId=$remoteResourceId _videoUrlOrPath=$_videoUrlOrPath videoName=$videoName isLocalVideo=$isLocalVideo");
    if (isLocalVideo) {
      checkVideo(_videoUrlOrPath);
      await videoKit.open(_videoUrlOrPath);
    }
    _initListener();
    fetchDetailData();
    hasMicPermission = await Permission.microphone.isGranted;
    DataCenterTimeManager().resetTime();
  }

  @override
  void onReady() {
    super.onReady();
    videoKit.exitNativeFullscreen();
  }

  void _initListener() async {
    subscriptions.clear();
    subscriptions.addAll([
      videoKit.currentSubtitleIndex.listen((index) async {
        logger("currentSubtitleIndex listen currentSubtitleIndex= $index _pageChangeByUser=${pageChangeByUser.value}");
        currentPage.value = index;
        if (videoKit.openLsMode.value) {
          if (!pageChangeByUser.value) {
            //如果是用户自己滑动 不需要做任何处理
            // 当更改了进度之后 也需要stopPlayer
            //1. 当用户主动滑动的时候，如果出现了跳过的逻辑，是需要触发这里的jump的
            recordSoundPlayer.stopPlayer();
            _recognitionService.stopRecord();
            jumpToPage(index);
            recordingInLsMode.value = false;
          }
        } else {
          subtitleListScrollToCenter();
        }
      }),
      videoKit.seekSkipSubtitleIndex.listen((index) {
        jumpToPage(index);
      }),
      videoKit.playing.listen((playing) {
        if (playing) {
          DataCenterTimeManager().resume();
        }
      }),
      videoKit.getPlayer.stream.position.listen((duration) {
        if (canChangeSeekBarRatio) {
          seekBarRatio.value = totalDuration.value == 0 ? 0 : duration.inMilliseconds / totalDuration.value;
          debugPrint("duration.inMilliseconds =${duration.inMilliseconds} totalDuration=$totalDuration seekBarRatio=${seekBarRatio.value}");
        }
      }),
      videoKit.getPlayer.stream.duration.listen((event) {
        totalDuration.value = event.inMilliseconds;
      }),
      videoKit.pausedInLsMode.listen((paused) {
        logger("handleLsMode paused=$paused");
        if (paused && Config().playerConfig.autoRecord) {
          if (DateTime.now().millisecondsSinceEpoch - videoKit.onHorizontalDragEndTime < 1000) {
            logger("handleLsMode close autoRecord cause time offset < 1000");
            videoKit.onHorizontalDragEndTime = 0;
            return;
          }
          recordStart();
        }
      }),
      videoKit.seekByHistoryIndex.listen((index) {
        //这里为了等待pageview先跳转到指定的位置 保证不会跳到第一句然后又跳到对应的进度所对应的index
        jumpToPage(index);
        loadingSubtitle.value = false;
      }),
      ObsUtil().loginStatus.listen((isLogin) {
        if (isLogin) {
          fetchDetailData();
        }
      }),
      Xfyun().completed.listen((data) {
        logger("Xfyun completed = $data");
        if (Xfyun().isCompleted(localDetailResp?.resourceId)) {
          loadingSubtitle.value = true;
          fetchDetailData();
        }
      }),
      ObsUtil().uploadSubtitle.listen((v) async {
        logger("receive uploadSubtitle obs");
        loadingSubtitle.value = true;
        localDetailResp = v;
        await loadSubtitle();
        loadingSubtitle.value = false;
      }),
    ]);
    initVoiceListen();
  }

  void checkVideo(String videoPath) async {
    var videoInfo = await FFmpegUtils().getVideoInfo(videoPath);
    logger("videoInfo width=${videoInfo['width']} height=${videoInfo['height']} frameRate=${videoInfo['frameRate']}");
    if (FFmpegUtils().isVideoOver4K(videoInfo['width'] ?? 0, videoInfo['height'] ?? 0)) {
      videoKit.pause();
      Get.dialog(
        CommonDialog(title: "视频分辨率大于等于4K,会影响体验\n是否进行视频压缩？", options: const [
          "确定"
        ], callbacks: [
          () => {
                VideoCompressionUtil().addCompress(VideoCompression(videoPath: videoPath)),
                Get.back(),
              }
        ]),
        barrierDismissible: false,
      );
    }
  }

  Future initVoiceListen() async {
    // 初始化语音识别服务
    await _recognitionService.initRecorder();
    // 语音识别回调
    // 注意：必须在 [speechRecognition] 方法调用之前监听
    _recognitionService.onRecordResult().listen((message) {
      // 语音识别成功，结果为 message
      recordText.value = message;
      // var score = lsWordsCompare(videoKit.subtitles[videoKit.currentSubtitleIndex.value].targetData, recordText.value).score.toInt();
      // SPUtil().saveRecord(
      //   filterVideoUrl(_videoUrlOrPath),
      //   localRecordPath: _recordPath,
      //   subtitleIndex: videoKit.currentSubtitleIndex.value,
      //   score: score.toInt(),
      //   userString: recordText.value,
      // );
      _updateRecordMap();
    }, onError: (err) {});

    _recognitionService.onStopRecording().listen((isAutomatic) {
      if (isAutomatic) {
        // 录音时间到达最大值60s，自动停止
        "录音时间到达最大值60s，自动停止".toast;
        recordStop(forceThrowRecord: true);
      } else {
        // 主动调用 stopRecord，停止录音
      }
      videoKit.unmute();
    });
    await recordSoundPlayer.openPlayer();
  }

  void closeLsMode() {
    videoKit.openLsMode.value = false;
    subtitleListScrollToCenter();
  }

  void openLsMode() {
    videoKit.openLsMode.value = true;
    jumpToPage(videoKit.currentSubtitleIndex.value);
  }

  void jumpToPage(int index) {
    if (isLandscape.value) {
      return;
    }
    if (index >= 0 && index < videoKit.subtitles.length) {
      if (!pageController.hasClients) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          pageController.jumpToPage(index);
        });
      } else {
        pageController.jumpToPage(index);
      }
    }
  }

  void jumpPreSubtitle() {
    videoKit.skipFindReverse = true;
    videoKit.preSubtitle();
    videoKit.play();
    _updateRecordMap();
  }

  void jumpNextSubtitle() {
    videoKit.skipFindReverse = false;
    videoKit.nextSubtitle();
    videoKit.play();
    _updateRecordMap();
  }

  void backWhenFullscreen() {
    videoKit.exitNativeFullscreen();
  }

  void toggleSubtitlePlaceholder() {
    showSubtitlePlaceholder.value = !showSubtitlePlaceholder.value;
  }

  void recordStart() async {
    if (!hasMicPermission) {
      Map<Permission, PermissionStatus> statuses = await [
        Permission.microphone,
      ].request();
      // 检查每个权限的状态
      if (statuses[Permission.microphone]?.isGranted == false) {
        requestVoicePermission(title: "需要录音权限");
        return;
      }
    }

    _recordStart();
  }

  void requestVoicePermission({String title = "需要录音和语音识别权限"}) {
    if (Platform.isIOS || Platform.isAndroid) {
      CommonDialog(title: title, options: const [
        "跳转"
      ], callbacks: [
        () => {
              openAppSettings(),
            },
      ]).showDialog;
    }
  }

  void _recordStart() async {
    logger("_recordStart playingInLsMode to false");
    await videoKit.mute();
    // await videoKit.pause();
    if (videoKit.playing.value || recordSoundPlayer.isPlaying) {
      await recordSoundPlayer.stopPlayer();
    }

    videoKit.playing.value = false;
    _recordPath = await FileUtils().getStringByDir(FileUtils().getRecordDir());
    _recordPath = "$_recordPath/${basenameWithoutExtension(filterVideoUrl(_videoUrlOrPath))}${videoKit.currentSubtitleIndex.value}.aac";
    logger("recordStart _recordPath = $_recordPath");

    recordingInLsMode.value = true;
    await _recognitionService.configSession();
    logger("dingSoundPath=$dingSoundPath");
    // await recordSoundPlayer.startPlayer(
    //   fromURI: dingSoundPath,
    //   codec: Codec.mp3,
    //   whenFinished: () async{
    //     await recordSoundPlayer.stopPlayer();
    //     _recognitionService.startRecord(_recordPath);
    //   },
    // );
    playCurrentLsIndex();
    _recognitionService.startRecord(_recordPath);
  }

  void recordStop({bool forceThrowRecord = false}) async {
    logger("recordStop");
    videoKit.pause();
    recordingInLsMode.value = false;
    await _recognitionService.stopRecord();
    if (forceThrowRecord) {
      logger("recordStop forceThrowRecord return");
      return;
    }
    _recognitionService.speechRecognition();
    if (_recognitionService.getRecordingUint8ListData().isEmpty) {
      toastInfoError("录音时间太短");
      return;
    }
    await _recognitionService.saveVoiceData2File(_recordPath);
    // await FFmpegUtils().reduceNoise(_recordPath);
    playRecordWhenRecordEnd(_recordPath);
    if (Config().playerConfig.showSubtitleWhenRecordEnd) {
      showSubtitlePlaceholder.value = false;
    }
  }

  void playRecordWhenRecordEnd(String path) async {
    if (Config().playerConfig.autoPlayRecordWhenRecordEnd) {
      try {
        //可能是调用了recordSoundPlayer的stop之后 还是有一些时间需要回收
        //不加就会导致偶尔出现录制结束后播放录音只播放了一点点
        await Future.delayed(const Duration(milliseconds: 350));
        await videoKit.pause();
        recordSoundPlayer.startPlayer(
          fromURI: _recordPath,
          whenFinished: () {
            recordSoundPlayer.stopPlayer();
          },
        );
      } catch (e) {
        logger("recordStop 录音播放失败: $e");
      }
    }
  }

  //播放录音的时候需要暂停视频
  void playRecord() async {
    await videoKit.mute();
    playCurrentLsIndex();
    var index = getCurrentSubtitleIndex();
    try {
      var localRecordPath = recordIndexMap[index]?.localRecordPath;
      logger("localRecordPath =$localRecordPath");
      if (localRecordPath != null) {
        recordSoundPlayer.startPlayer(
          fromURI: localRecordPath,
          whenFinished: () {
            recordSoundPlayer.stopPlayer();
            videoKit.unmute();
          },
        );
      } else {
        toastInfo("你还没有录制的声音");
      }
    } catch (e) {
      logger("录音播放失败: $e");
    }
  }

  void _updateRecordMap() {
    var localRecordList = SPUtil().getRecordList(filterVideoUrl(_videoUrlOrPath));
    recordIndexMap.value = _extractRecordPaths(localRecordList);
    currentHasRecordFile.value = recordIndexMap[getCurrentSubtitleIndex()] != null;
    recordText.value = recordIndexMap[videoKit.currentSubtitleIndex.value]?.userString ?? "";
  }

  Map<int, LocalRecordModel> _extractRecordPaths(List<LocalRecordModel> records) {
    Map<int, LocalRecordModel> recordPaths = {};
    for (LocalRecordModel record in records) {
      if (record.subtitleIndex != null && record.localRecordPath != null) {
        recordPaths[record.subtitleIndex!] = record;
      }
    }
    return recordPaths;
  }

  int getCurrentSubtitleIndex() {
    if (isLandscape.value) {
      return videoKit.currentSubtitleIndex.value;
    }
    if (!videoKit.openLsMode.value) {
      return videoKit.currentSubtitleIndex.value;
    }
    if (pageController.hasClients) {
      return pageController.page?.toInt() ?? 0;
    }
    return 0;
  }

  //如果正在播放 那就暂停
  //如果已经暂停就继续播放 如果暂停的时候停留的时间和当前字幕的结尾 那么从头开始播放
  void lsPlayClick() async {
    logger("lsPlayClick");
    if (!isLandscape.value && pageController.hasClients && pageController.page == null && videoKit.openLsMode.value) {
      logger("lsPlayClick pageController.page null");
      return;
    }
    await _recognitionService.stopRecord();
    playCurrentLsIndex();
  }

  Future<void> playCurrentLsIndex() async {
    var index = getCurrentSubtitleIndex();
    logger("playCurrentLsIndex playingInLsMode = ${videoKit.playing.value} index=$index");
    if (videoKit.isPlaying()) {
      logger("playCurrentLsIndex pause when playingInLsMode to false");
      videoKit.pause();
    } else {
      if (videoKit.currentDurationIsMatchCurrentSubtitleEndInLsMode(index: index)) {
        logger("playCurrentLsIndex seekBySubtitleIndex index=$index");
        recordText.value = "";
        await videoKit.seekBySubtitleIndex(index);
        await videoKit.play();
      } else {
        logger("playCurrentLsIndex play");
        videoKit.play();
      }
    }
  }

  void lsContainerClick(int index) async {
    recordStop(forceThrowRecord: true);
    recordText.value = "";
    await videoKit.seekBySubtitleIndex(index);
    await videoKit.play();
  }

  void switchSpeed() async {
    await videoKit.switchSpeed();

    lsContainerClick(currentPage.value);
    showCurrentLandScapeToast.value = true;
    currentToast.value = "已切换至${videoKit.currentSpeed.value}倍数";
    await Future.delayed(Duration(seconds: 1));
    showCurrentLandScapeToast.value = false;
  }

  void onUserScrollStart() {
    pageChangeByUser.value = true;
  }

  void onPageScrollDirection(bool isForward) {
    videoKit.skipFindReverse = !isForward;
  }

  void onUserScrollEnd() {
    pageChangeByUser.value = false;
  }

  void onUserScroll(UserScrollNotification userScroll) {
    videoKit.skipFindReverse = userScroll.direction == ScrollDirection.forward;
  }

  //改变index的时候 需要暂停视频、关闭录音 关闭录音播放
  //当更改了进度之后 也需要stopPlayer
  void onPageChanged(int index) async {
    currentPage.value = index;
    logger("onPageChanged index = $index  _pageChangeByUser= ${pageChangeByUser.value}");
    currentHasRecordFile.value = recordIndexMap[index] != null;

    if (!pageChangeByUser.value) {
      return;
    }
    if (recordingInLsMode.value) {
      _recognitionService.stopRecord();
    }
    videoKit.unmute();
    recordSoundPlayer.stopPlayer();

    logger("onPageChanged pause");
    videoKit.pause(); //为了解决显示loading的问题 先pause 再play
    recordingInLsMode.value = false;
    onSubTitleListClick(index);
  }

  void onSubTitleListClick(int index) {
    recordText.value = "";
    videoKit.seekBySubtitleIndex(index);
    videoKit.play();
  }

  void goMoreSetting() {
    videoKit.pause();
    _recognitionService.stopRecord();
    recordSoundPlayer.stopPlayer();
    recordingInLsMode.value = false;
    Get.bottomSheet(
      const PlayerMoreWidget(),
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      elevation: 2,
      barrierColor: Colors.black.withOpacity(0.5),
      enableDrag: true,
    );
  }

  void goEditSubtitle() async {
    if (localDetailResp == null) {
      Get.toNamed(Routes.LOGIN);
      return;
    }

    Get.toNamed(Routes.SUBTITLE_EDIT, arguments: {
      'subtitleUrl': localDetailResp!.subtitleUrl,
      'resourceId': localDetailResp!.resourceId,
      'skipList': localDetailResp!.skipList,
      'resourceType': localDetailResp!.resourceType,
      'index': getCurrentSubtitleIndex(),
      'nativeLangSubtitleUrl': localDetailResp?.nativeLangSubtitleUrl,
      'originSubtitleUrl': localDetailResp?.originSubtitleUrl,
    });
  }

  void goSubtitleSearch() async {
    var subtitleAddResult = await Get.toNamed(Routes.SUBTITLE_ADD, arguments: {'videoPath': _videoUrlOrPath});
    if (subtitleAddResult != null) {
      var subtitlePath = subtitleAddResult['subtitlePath'];
      logger("DetailController goSubtitleSearch subtitlePath=$subtitlePath");
      whenSubtitleAdd(subtitlePath);
    }
  }

  void goAIGenSubtitle() async {
    Get.toNamed(Routes.AUDIO_CONVERT, arguments: {
      'videoPath': _videoUrlOrPath,
      'localResourceId': localDetailResp?.resourceId,
    });
  }

  void getSubtitleByVideo() async {
    var subtitlePath = await videoKit.getSubtitleByVideo(_videoUrlOrPath);
    if (subtitlePath.isEmpty) {
      "暂时无法解析出目录".toast;
      return;
    }
    whenSubtitleAdd(subtitlePath);
  }

  Future<void> whenSubtitleAdd(String subtitleUrl) async {
    SPUtil().saveHistory(filterVideoUrl(_videoUrlOrPath), subtitleLocalPath: subtitleUrl);
    videoKit.resetLsModeIndex(needPlay: false);
    await videoKit.loadSubtitles(subtitleUrl);
    logger("loadSubtitle uploadSubtitle $subtitleUrl  whenSubtitleAdd");
    await SubtitleUtil().uploadSubtitle(
      subtitleUrl,
      localDetailResp?.resourceId,
      localDetailResp?.resourceType,
      autoAddSkipWhenSubtitleTargetNone: true,
      needRemoveSubtitleUrl: localDetailResp?.subtitleUrl,
    );
  }

  void subtitleListScrollToCenter() {
    if (isLandscape.value) {
      return;
    }
    if (!_allowAutoScroll) {
      return;
    }
    itemScrollController.scrollToIndex(currentPage.value, preferPosition: AutoScrollPosition.middle);
  }

  void _scrollListener() {
    if (itemScrollController.position.userScrollDirection == ScrollDirection.reverse) {
      // 向上滑动，隐藏 AppBar
      isAppBarVisible.value = false;
    } else if (itemScrollController.position.userScrollDirection == ScrollDirection.forward) {
      isAppBarVisible.value = true;
    }
  }

  void onPointerDown() {
    _autoScrollTimer?.cancel();
    _allowAutoScroll = false;
  }

  void onPointerUp() {
    _autoScrollTimer = Timer(const Duration(seconds: 3), () {
      _allowAutoScroll = true;
    });
  }

  @override
  void didChangeMetrics() {
    _recognitionService.sessionLost();
    // ignore: deprecated_member_use
    final metrics = WidgetsBinding.instance.window.physicalSize;
    final width = metrics.width;
    final height = metrics.height;
    isLandscape.value = width > height;
    if (!isLandscape.value) {
      jumpToPage(videoKit.currentSubtitleIndex.value);
    } else {
      lsContainerClick(videoKit.currentSubtitleIndex.value);
    }
  }

  void onVisibilityLost() {
    DataCenterTimeManager().pause();
    videoKit.pause();
    _recognitionService.stopRecord();
    recordSoundPlayer.stopPlayer();
    recordingInLsMode.value = false;
  }

  void onVisibilityGained() {
    DataCenterTimeManager().resume();
  }

  void goNoteList() async {
    Get.toNamed(Routes.NOTELIST, arguments: {
      'subtitlePath': videoKit.subtitlePath,
    });
  }

  void showAddNoteDialog() {
    int index = currentPage.value;
    if (localDetailResp == null) {
      Get.toNamed(Routes.LOGIN);
      return;
    }
    Get.bottomSheet(
      AddNoteWidget(
        subtitle: videoKit.subtitles[index],
        noteId: localDetailResp!.notes?.firstWhereOrNull((element) => element.id == videoKit.subtitles[index].subtitleIndex)?.id ?? "",
        noteSaveCallback: (type) {
          if (type == 2) {
            fetchDetailData();
          }
        },
        resourceType: localDetailResp!.resourceType!,
        resourceId: localDetailResp!.resourceId!,
        videoStartTime: videoKit.subtitles[index].start.inMilliseconds,
        videoEndTime: videoKit.subtitles[index].end.inMilliseconds,
      ),
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      elevation: 2,
      barrierColor: Colors.black.withOpacity(0.5),
      enableDrag: true,
    );
  }

  Future<void> fetchDetailData() async {
    if (!isLogin()) {
      return loadSubtitle();
    }
    await Net.getRestClient().getVideoDetail({
      'localVideoPath': isLocalVideo ? _videoUrlOrPath : "",
      'fileName': isLocalVideo ? basenameWithoutExtension(filterVideoUrl(_videoUrlOrPath)) : "",
      'resourceId': remoteResourceId,
      'resourceType': isLocalVideo ? 2 : 1,
    }).then((v) async {
      logger("getVideoDetail success resp=${jsonEncode(v.data)}");
      localDetailResp = v.data;
      if (!isLocalVideo) {
        _videoUrlOrPath = localDetailResp!.playUrl ?? "";
        var cacheVideoFile = await fcm.DefaultCacheManager().getFileFromCache(filterVideoUrl(_videoUrlOrPath));

        if (cacheVideoFile == null) {
          logger("getVideoDetail cacheVideoFile is null start download");
          await videoKit.open(_videoUrlOrPath);
          downloadVideoCache(_videoUrlOrPath);
        } else {
          _videoUrlOrPath = cacheVideoFile.file.path;
          logger("getVideoDetail cacheVideoFile is not null,use cache path=$_videoUrlOrPath");
          await videoKit.open(_videoUrlOrPath);
        }
      }
      DataCenterTimeManager().begin(localDetailResp?.resourceId, localDetailResp?.resourceType, localDetailResp?.currentLsTimes);
      if (videoKit.positionInit == -1) {
        videoKit.positionInit = localDetailResp!.position ?? 0;
      }
      await loadSubtitle();
    }).catchError((err) async {
      loadSubtitleWhenError(err.toString());
    });
  }

  //处理和字幕相关的逻辑
  void relationSubtitle2IndexMap() {
    logger("processRelationSubtitle");
    notesMap.clear();
    if (localDetailResp!.notes != null) {
      processNotesIntervals2Map(localDetailResp!.notes!, notesMap, videoKit.subtitles);
    }

    if (localDetailResp!.sentenceCollects != null) {
      processIntervals2Map(localDetailResp!.sentenceCollects!, sentenceCollectMap, videoKit.subtitles);
    }
    videoKit.skipList = getIndexsFromIntervalList(localDetailResp?.skipList ?? [], videoKit.subtitles);
    logger("processRelationSubtitle videoKit.skipList=${videoKit.skipList}");
  }

  Future<void> loadSubtitleWhenError(String err) async {
    loadingSubtitle.value = false;
    logger("loadSubtitle error=$err");
    videoKit.play();
    _updateRecordMap();
  }

  Future<void> loadSubtitle() async {
    logger("loadSubtitle start");
    var subtitlePath = "";
    subtitlePath = localDetailResp!.subtitleUrl!;
    logger("loadSubtitle subtitlePath from remote $subtitlePath");
    if (isLocalVideo && subtitlePath.isEmpty && localDetailResp!.nativeLangSubtitleUrl != "") {
      //说明没有从服务端获取到字幕文件 默认使用历史保存的
      var localHistoryModel = await SPUtil().getHistory(_videoUrlOrPath);
      logger("loadSubtitle localHistoryModel=${jsonEncode(localHistoryModel)}");
      if (localHistoryModel == null || localHistoryModel.subtitleLocalPath == null || localHistoryModel.subtitleLocalPath?.isEmpty == true) {
        SPUtil().saveHistory(_videoUrlOrPath);
        logger("loadSubtitle localHistoryModel is null");
        subtitlePath = await videoKit.getSubtitleByVideo(_videoUrlOrPath);
      } else {
        subtitlePath = localHistoryModel.subtitleLocalPath ?? "";
      }
    }
    logger("loadSubtitle subtitlePath final load $subtitlePath");
    if (shouldUseDoubleSubtitle(localDetailResp)) {
      logger("loadSubtitle remoteresource load target ${localDetailResp!.originSubtitleUrl} and native ${localDetailResp!.nativeLangSubtitleUrl} ");
      await videoKit.loadSubtitles(localDetailResp!.originSubtitleUrl ?? "", nativeSubtitlePath: localDetailResp!.nativeLangSubtitleUrl ?? "");
    } else {
      await videoKit.loadSubtitles(subtitlePath);
    }
    logger("loadSubtitle finish");
    _updateRecordMap();
    relationSubtitle2IndexMap();
    // videoKit.delaySubtitle(localHistoryModel.subtitleDelayInMilliseconds);
  }

  void switchCollect() {
    int index = currentPage.value;
    if (localDetailResp == null) {
      Get.toNamed(Routes.LOGIN);
      return;
    }
    var isRemove = sentenceCollectMap[index] != null;
    var videoTimeInterval =
        VideoTimeInterval(start: videoKit.subtitles[index].start.inMilliseconds, end: videoKit.subtitles[index].end.inMilliseconds);

    if (isRemove) {
      sentenceCollectMap[index] = null;
      sentenceCollectMap.refresh();
      Net.getRestClient().removeLocalSencenceCollect({
        'resourceId': localDetailResp!.resourceId,
        'resourceType': localDetailResp!.resourceType,
        'times': [videoTimeInterval],
      });
    } else {
      sentenceCollectMap[index] = videoTimeInterval;
      sentenceCollectMap.refresh();
      Net.getRestClient().addLocalSencenceCollect({
        'resourceId': localDetailResp!.resourceId,
        'resourceType': localDetailResp!.resourceType,
        'times': [videoTimeInterval],
      });
    }
  }

  void updateLocalDetail(int position) async {
    if (localDetailResp == null) {
      return;
    }
    Net.getRestClient().updateVideoDetail({
      'resourceId': localDetailResp!.resourceId,
      'resourceType': localDetailResp!.resourceType,
      'position': position,
    });
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      DataCenterTimeManager().resume();
    } else {
      _recognitionService.sessionLost();
      DataCenterTimeManager().pause();
    }
  }

  void addDataEpisode() async {
    logger("addDataEpisode start");
    if (localDetailResp == null) {
      logger("addDataEpisode localDetailResp null return");
      return;
    }
    var duration = DataCenterTimeManager().getDuration();
    var startTime = DataCenterTimeManager().startTime;
    var endTime = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    logger("addDataEpisode duration =$duration startTime=$startTime endTime=$endTime");

    if (duration > 60 * 60 * 20) {
      toastInfoError("时长过长");
      return;
    }
    var learnDuration = (duration / 60).round();
    if (learnDuration < 1) {
      logger("addDataEpisode 小于一分钟，不计入");
      return;
    }
    logger("addDataEpisode 本次已学习$learnDuration分钟");
    toastInfo("本次已学习$learnDuration分钟");

    Net.getRestClient().addDataEpisode({
      'resourceId': localDetailResp!.resourceId,
      'resourceType': localDetailResp?.resourceType,
      'learnDuration': learnDuration,
      'startTime': startTime,
      'endTime': endTime,
      'currentLsTimes': (localDetailResp!.currentLsTimes ?? 0)
    }).then((value) {
      logger("addDataEpisode success");
      DataCenterTimeManager().clear();
      ObsUtil().updateDataCenter.value = DateTime.now().millisecond;
    });
  }

  void showLsTimeDialog() {
    showLsTimeModeDialog(localDetailResp!.resourceId, 2);
  }

  void changeControlMenuSort(int newIndex, int oldIndex) {
    if (newIndex > oldIndex) newIndex -= 1;
    final item = playerMenuItems.removeAt(oldIndex);
    playerMenuItems.insert(newIndex, item);
    Config().playerConfig.menuSort = playerMenuItems.map((item) => item.id).toList();
    Net.getRestClient().updatePlayerConfig({'menuSort': Config().playerConfig.menuSort});
  }

  void onSliderChange(double ratio) async {
    canChangeSeekBarRatio = false;
    await videoKit.seek(Duration(milliseconds: (ratio * totalDuration.value).toInt()));
    await videoKit.onVideoSeekChangeEnd();
    canChangeSeekBarRatio = true;
  }

  bool _interceptorPop(bool stopDefaultButtonEvent, RouteInfo info) {
    if (Get.context?.isLandscape == true) {
      videoKit.toggleFullscreen();
      return false;
    }

    return true;
  }

  @override
  void onClose() async {
    BackButtonInterceptor.remove(_interceptorPop);
    videoKit.exitNativeFullscreen();
    logger("DetailController onClose\n position=${videoKit.currentPositionInMilliseconds} resourceId=${localDetailResp?.resourceId}");
    updateLocalDetail(videoKit.currentPositionInMilliseconds);
    addDataEpisode();
    SPUtil().saveSubtitleCover(_videoUrlOrPath, Config().subtitleCoverModel);
    ObsUtil().watchHistoryPositionChange.value = [localDetailResp?.resourceId ?? "", videoKit.currentPositionInMilliseconds];
    _recognitionService.dispose();
    WidgetsBinding.instance.removeObserver(this);
    await recordSoundPlayer.closePlayer();
    videoKit.destory();
    super.onClose();
  }
}

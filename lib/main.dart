// import 'package:firebase_core/firebase_core.dart';
// import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get_storage/get_storage.dart';
import 'package:lsenglish/config/config.dart';
import 'package:lsenglish/utils/file.dart';
import 'package:lsenglish/video/player.dart';
import 'net/net.dart';
import 'my_app.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  // Net.configureDio(baseUrl: "http://127.0.0.1:8000/");
  // Config().isDev = true;
  if (kReleaseMode) {
    Net.configureDio(baseUrl: "https://test.seedtu.com/");
  } else if (Config().isDev) {
    Net.configureDio(baseUrl: "https://test.seedtu.com/");
    // Net.configureDio(baseUrl: "http://192.168.1.110:3000/");
    // Net.configureDio(baseUrl: "http://192.168.31.172:3000/");
  } else {
    // Net.configureDio(baseUrl: "http://127.0.0.1:3000/");
    // Net.configureDio(baseUrl: "http://192.168.31.172:3000/");
    Net.configureDio(baseUrl: "https://test.seedtu.com");
  }
  await GetStorage.init();
  // if (kReleaseMode) {
  //   await Firebase.initializeApp(
  //     options: DefaultFirebaseOptions.currentPlatform,
  //   );
  //   FlutterError.onError = (errorDetails) {
  //     FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
  //   };
  //   PlatformDispatcher.instance.onError = (error, stack) {
  //     FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
  //     return true;
  //   };
  // }

  await FileUtils().ensureDirectory();
  Config().init();
  await IPlayer.ensureInitialized();

  SystemChrome.setEnabledSystemUIMode(
    SystemUiMode.manual,
    overlays: SystemUiOverlay.values,
  ).then((_) {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
    ]).then((_) {
      runApp(const MyApp());
    });
  });
}

// import 'package:flutter/material.dart';
// import 'package:receive_sharing_intent/receive_sharing_intent.dart';

// void main() => runApp(MyApp());

// class MyApp extends StatefulWidget {
//   @override
//   _MyAppState createState() => _MyAppState();
// }

// class _MyAppState extends State<MyApp> {
//   var files = "等待接收";
//   @override
//   void initState() {
//     super.initState();

//     // 监听共享来的视频文件
//     receive();
//   }

//   void receive() {
//     ReceiveSharingIntent.instance.getMediaStream().listen((List<SharedMediaFile> value) {
//       if (value.isNotEmpty) {
//         // 获取视频文件路径
//         String path = value.first.path;
//         setState(() {
//           files = path;
//         });
//       }
//     }, onError: (err) {
//       print("获取共享内容出错: $err");
//     });

//     // 处理应用关闭时收到的意图
//     ReceiveSharingIntent.instance.getInitialMedia().then((List<SharedMediaFile> value) {
//       print(value);
//       if (value.isNotEmpty) {
//         String path = value.first.path;
//         setState(() {
//           files = path;
//         });
//       }
//     }, onError: (err) {
//       print("111获取共享内容出错: $err");
//     });
//   }

//   @override
//   Widget build(BuildContext context) {
//     return MaterialApp(
//       home: Scaffold(
//         body: Center(
//             child: GestureDetector(
//                 onTap: () {
//                   receive();
//                 },
//                 child: Text(files))),
//       ),
//     );
//   }
// }

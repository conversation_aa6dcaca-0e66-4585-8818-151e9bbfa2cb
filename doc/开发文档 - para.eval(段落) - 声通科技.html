<!DOCTYPE html>
<html lang="en">
	<head>
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
		<meta charset="utf-8" />
		<title>开发文档 - para.eval(段落)
 - 声通科技</title>

		<meta name="description" content="overview &amp; stats" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />

		<link rel="shortcut icon" href="/favicon.png">

		<!-- bootstrap & fontawesome -->
		<link rel="stylesheet" href="/assets/css/bootstrap.min.css" />
		<link rel="stylesheet" href="/components/font-awesome/css/font-awesome.css" />

		<!-- page specific plugin styles -->
		<link rel="stylesheet" href="/components/jquery.gritter/css/jquery.gritter.min.css" />

		<!-- text fonts -->
		<link rel="stylesheet" href="/assets/css/ace-fonts.min.css" />

		<!-- ace styles -->
		<link rel="stylesheet" href="/assets/css/ace.min.css" class="ace-main-stylesheet" id="main-ace-style" />

		<!--[if lte IE 9]>
			<link rel="stylesheet" href="assets/css/ace-part2.css" class="ace-main-stylesheet" />
		<![endif]-->
		<!--<link rel="stylesheet" href="/assets/css/ace-skins.css" />-->

		<!--[if lte IE 9]>
		  <link rel="stylesheet" href="/assets/css/ace-ie.css" />
		<![endif]-->

		<!-- inline styles related to this page -->


		<!-- HTML5shiv and Respond.js for IE8 to support HTML5 elements and media queries -->

		<!--[if lte IE 8]>
		<script src="/components/html5shiv/dist/html5shiv.min.js"></script>
		<script src="/components/respond/dest/respond.min.js"></script>
        <![endif]-->
        <link rel="stylesheet" href="/assets/css/app.css" />
        
	</head>

	<body class="no-skin">
		<!-- #section:basics/navbar.layout -->
		<div id="navbar" class="navbar navbar-default ace-save-state navbar-fixed-top">
			<div class="navbar-container ace-save-state" id="navbar-container">
				<!-- #section:basics/sidebar.mobile.toggle -->
				
				<button type="button" class="navbar-toggle menu-toggler pull-left" id="menu-toggler" data-target="#sidebar">
					<span class="sr-only">Toggle sidebar</span>

					<span class="icon-bar"></span>

					<span class="icon-bar"></span>

					<span class="icon-bar"></span>
				</button>
				

				<!-- /section:basics/sidebar.mobile.toggle -->
				<div class="navbar-header pull-left">
					<!-- #section:basics/navbar.layout.brand -->
					<a href="/" class="navbar-brand">
						<small>
							<!--<i class="fa fa-leaf"></i>-->
							声通科技
						</small>
					</a>
					<!-- /section:basics/navbar.layout.brand -->

					<!-- #section:basics/navbar.toggle -->

					<!-- /section:basics/navbar.toggle -->
				</div>

				<!-- #section:basics/navbar.dropdown -->
				<div class="navbar-buttons navbar-header pull-right" role="navigation">
					<ul class="nav ace-nav">
						<li class="light-blue li-nav-search">
							<div class="nav-search" id="nav-search" style="top:5px;">
								<form class="form-search">
									<span class="input-icon input-icon-right">
										<input type="text" placeholder="搜索" class="nav-search-input" style="width: 220px;height: 35px !important;color: #deebff !important;background: rgba(9,30,66,0.48);" data-action="nav-search-input" autocomplete="off">
										<i class="ace-icon fa fa-search nav-search-icon" style="top: 4px;"></i>
									</span>
								</form>
							</div>
						</li>
						
						<!-- #section:basics/navbar.user_menu -->
						<li class="light-blue dropdown-modal" style="float: right !important;">
							<a data-toggle="dropdown" href="#" class="dropdown-toggle">
								<!--<img class="nav-user-photo" src="/assets/avatars/user.jpg" alt="Jason's Photo" />-->
								<span class="user-info">
									<small>Welcome,</small>
									<span>seedtu</span>
								</span>
								<i class="ace-icon fa fa-caret-down"></i>
							</a>
							<ul class="user-menu dropdown-menu-right dropdown-menu dropdown-yellow dropdown-caret dropdown-close">
								<!-- <li>
									<a href="#">
										<i class="ace-icon fa fa-cog"></i>
										设置
									</a>
								</li> -->

								<li>
									<a data-id="634" data-name="seedtu" data-email="<EMAIL>"  data-action='edit-user'>
										<i class="ace-icon fa fa-user"></i>
										修改
									</a>
								</li>

								<li class="divider"></li>

								<li>
									<a href="/account/logout">
										<i class="ace-icon fa fa-power-off"></i>
										登出
									</a>
								</li>
							</ul>
						</li>

						<!-- /section:basics/navbar.user_menu -->
					</ul>
				</div>

				<!-- /section:basics/navbar.dropdown -->
			</div><!-- /.navbar-container -->
		</div>

		<!-- /section:basics/navbar.layout -->
		<div class="main-container ace-save-state" id="main-container">
			<script type="text/javascript">
				//try{ace.settings.loadState('main-container')}catch(e){}
			</script>

			<!-- #section:basics/sidebar -->
			<div id="sidebar" class="sidebar responsive ace-save-state sidebar-fixed sidebar-scroll">
				<ul class="nav nav-list" style="top: 0px;"><li class=""><a href="/index"><span class="menu-text"> 首页 </span></a><b class="arrow"></b></li><li class=""><a href="#" class="dropdown-toggle"><span class="menu-text">系统管理</span><b class="arrow fa fa-angle-down"></b></a><b class="arrow"></b><ul class="submenu"><li class=""><a href="/mail_manager/index"><span>关注邮箱管理</span></a><b class="arrow"></b></li></ul></li><li class="active open"><a href="#" class="dropdown-toggle"><span class="menu-text">文档中心</span><b class="arrow fa fa-angle-down"></b></a><b class="arrow"></b><ul class="submenu"><li class=""><a href="#" class="dropdown-toggle"><span>SDK文档</span><b class="arrow fa fa-angle-down"></b></a><b class="arrow"></b><ul class="submenu"><li class=""><a href="#" class="dropdown-toggle"><span>Flutter SDK</span><b class="arrow fa fa-angle-down"></b></a><b class="arrow"></b><ul class="submenu"><li class=""><a href="/docs/index?id=267"><span>接入指南</span></a><b class="arrow"></b></li><li class=""><a href="#" class="dropdown-toggle"><span>开发指南</span><b class="arrow fa fa-angle-down"></b></a><b class="arrow"></b><ul class="submenu"><li class=""><a href="/docs/index?id=268"><span>相关类说明</span></a><b class="arrow"></b></li><li class=""><a href="/docs/index?id=269"><span>FlutterPluginSTkouyu</span></a><b class="arrow"></b></li><li class=""><a href="/docs/index?id=271"><span>KYEngineSetting</span></a><b class="arrow"></b></li><li class=""><a href="/docs/index?id=274"><span>KYRecordSetting</span></a><b class="arrow"></b></li><li class=""><a href="/docs/index?id=276"><span>OnPlayListener</span></a><b class="arrow"></b></li><li class=""><a href="/docs/index?id=275"><span>OnInitEngineListener</span></a><b class="arrow"></b></li><li class=""><a href="/docs/index?id=277"><span>OnRecordListener</span></a><b class="arrow"></b></li><li class=""><a href="/docs/index?id=272"><span>KYEngineType</span></a><b class="arrow"></b></li><li class=""><a href="/docs/index?id=270"><span>KYAudioType</span></a><b class="arrow"></b></li><li class=""><a href="/docs/index?id=280"><span>KYEngineStatus</span></a><b class="arrow"></b></li></ul></li></ul></li></ul></li><li class="active open"><a href="#" class="dropdown-toggle"><span>评测参数及结果说明</span><b class="arrow fa fa-angle-down"></b></a><b class="arrow"></b><ul class="submenu"><li class="active open"><a href="#" class="dropdown-toggle"><span>英文</span><b class="arrow fa fa-angle-down"></b></a><b class="arrow"></b><ul class="submenu"><li class=""><a href="/docs/index?id=48"><span>word.eval/word.eval.pro(单词)</span></a><b class="arrow"></b></li><li class=""><a href="/docs/index?id=117"><span>sent.eval/sent.eval.pro(句子)</span></a><b class="arrow"></b></li><li class="active"><a href="/docs/index?id=118"><span>para.eval(段落)</span></a><b class="arrow"></b></li></ul></li></ul></li><li class=""><a href="#" class="dropdown-toggle"><span>错误码</span><b class="arrow fa fa-angle-down"></b></a><b class="arrow"></b><ul class="submenu"><li class=""><a href="/docs/index?id=26"><span>通用SDK错误码</span></a><b class="arrow"></b></li><li class=""><a href="/docs/index?id=25"><span>服务端错误码</span></a><b class="arrow"></b></li></ul></li></ul></li><li class=""><a href="#" class="dropdown-toggle"><span class="menu-text">下载中心</span><b class="arrow fa fa-angle-down"></b></a><b class="arrow"></b><ul class="submenu"><li class=""><a href="#" class="dropdown-toggle"><span>SDK下载</span><b class="arrow fa fa-angle-down"></b></a><b class="arrow"></b><ul class="submenu"><li class=""><a href="/sdks/index?id=89"><span>通用SDK(3.0)</span></a><b class="arrow"></b></li></ul></li><li class=""><a href="#" class="dropdown-toggle"><span>demo下载</span><b class="arrow fa fa-angle-down"></b></a><b class="arrow"></b><ul class="submenu"><li class=""><a href="/sdks/index?id=103"><span>flutter_demo</span></a><b class="arrow"></b></li></ul></li></ul></li></ul>
				</ul><!-- /.nav-list -->

				<!-- /section:basics/sidebar.layout.minimize -->
			</div>
			<div class="ia-splitter-handle" title="双击还原间距"><div class="ia-splitter-handle-highlight splitter-icon-grab-handle"></div></div>
            

<div class="main-content">
    <div class="main-content-inner">
        <!-- #section:basics/content.breadcrumbs -->
        <div class="breadcrumbs ace-save-state adj" id="breadcrumbs">
            <ul class="breadcrumb">
                <li>
                    <i class="ace-icon fa fa-home home-icon"></i>
                    <a href="/">首页</a>
                </li>
                <li>开发文档</li>
                <li>评测参数及结果说明</li><li>英文</li>
                <li class="active">para.eval(段落)</li>
            </ul><!-- /.breadcrumb -->
            <div class="nav-search">

                <button class="btn btn-link" data-action='favourite' data-id='118'>关注</button>






            </div>
        </div>

        <!-- /section:basics/content.breadcrumbs -->
        <div class="page-content" style="background-color: #FFF !important">
            <!-- <div class="page-header">
                <div class="row">
                    <div class="col-md-12">
                        
                    </div>
                </div>
            </div> --><!-- /.page-header -->
            <div>
                <div class="row" style="margin-left:0px;margin-right:0px;">
                    <div class="col-md-10 content ck-content" id='content' style="padding-left:40px;padding-top: 24px;">

                            <h2><span style="color:rgb(0,0,0);"><strong>request参数</strong></span></h2><figure class="table"><table><tbody><tr><td style="text-align:center;"><span style="color:rgb(0,0,0);">字段</span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);">类型</span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);">是否必须</span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);">默认值</span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);">说明</span></td></tr><tr><td><span style="color:rgb(0,0,0);">coreType</span></td><td><span style="color:rgb(0,0,0);">字符串</span></td><td><span style="color:rgb(0,0,0);">是</span></td><td>&nbsp;</td><td><p><span style="color:rgb(0,0,0);">para.eval：英文段落评测</span></p><p><span style="color:hsl(0,75%,60%);"><strong>注意：允许最大录音时长 300s</strong></span></p></td></tr><tr><td><span style="color:rgb(0,0,0);">getParam</span></td><td><span style="color:rgb(0,0,0);">整型</span></td><td><span style="color:rgb(0,0,0);">否</span></td><td>1</td><td><span style="color:rgb(0,0,0);">返回结果是否含请求参数。可选值：1、0，0为关闭、1为开启</span></td></tr><tr><td><span style="color:rgb(0,0,0);">refText</span></td><td><span style="color:rgb(0,0,0);">字符串</span></td><td><span style="color:rgb(0,0,0);">是</span></td><td>&nbsp;</td><td><p><span style="color:rgb(0,0,0);">评测文本，</span><span style="color:hsl(0,75%,60%);"><strong>不超过1000个单词或音标，音标格式：/æ/</strong></span></p><p><span style="color:rgb(0,0,0);">支持任意音标评测，音标需与dict_type设置的类型一致（若dict_type设置为IPA88，refText需要传IPA88音标评测）</span></p><p><span style="color:rgb(0,0,0);">1. 48个元辅音 ，如： /</span><span style="color:rgb(57,57,57);">æ/、/p/</span></p><p><span style="color:rgb(57,57,57);">2. 48个元辅音的任意组合，如：/kl/、/suː/</span></p><p><span style="color:rgb(57,57,57);">3. 单词音标评测，支持多种书写方式 ，如：/ˈwɔːtə(r)/、/ʰwaɪ/、/bɜːʴθdeɪ/</span></p></td></tr><tr><td><span style="color:rgb(0,0,0);">attachAudioUrl</span></td><td><span style="color:rgb(0,0,0);">整型</span></td><td><span style="color:rgb(0,0,0);">否</span></td><td><span style="color:rgb(0,0,0);">0</span></td><td><span style="color:rgb(0,0,0);">返回结果是否含音频下载地址。可选值：1、0，0为关闭、1为开启。</span><span style="color:hsl(0,75%,60%);">提示：音频保留7天，如需长期保存，建议下载到自己服务器。</span></td></tr><tr><td><span style="color:rgb(0,0,0);">paragraph_need_word_score</span></td><td><span style="color:rgb(0,0,0);">整型</span></td><td><span style="color:rgb(0,0,0);">否</span></td><td><span style="color:rgb(0,0,0);">0</span></td><td><span style="color:rgb(0,0,0);">返回结果是否含单词维度。可选值：1、0，0为关闭、1为开启</span></td></tr><tr><td><span style="color:rgb(0,0,0);">agegroup</span></td><td><span style="color:rgb(0,0,0);">整型</span></td><td><span style="color:rgb(0,0,0);">否</span></td><td><span style="color:rgb(0,0,0);">3</span></td><td><p><span style="color:rgb(0,0,0);">年龄段支持可选值：1、2、3</span></p><p><span style="color:rgb(0,0,0);">1：3-6years old</span></p><p><span style="color:rgb(0,0,0);">2：6-12years old</span></p><p><span style="color:rgb(0,0,0);">3：&gt;12years old</span></p></td></tr><tr><td><span style="color:rgb(0,0,0);">slack</span></td><td><span style="color:rgb(0,0,0);">整型或浮点型</span></td><td><span style="color:rgb(0,0,0);">否</span></td><td><span style="color:rgb(0,0,0);">0</span></td><td><span style="color:rgb(0,0,0);">打分松紧度， 取值范围[-1,1]，正数加分负数减分，0.1加分幅度较小，1加分幅度较大，干预后影响各维度分数</span></td></tr><tr><td><span style="color:rgb(0,0,0);">scale</span></td><td><span style="color:rgb(0,0,0);">整型或浮点型</span></td><td><span style="color:rgb(0,0,0);">否</span></td><td>100</td><td><span style="color:rgb(0,0,0);">分制，取值范围(0,100]，影响各维度分数</span></td></tr><tr><td><span style="color:rgb(0,0,0);">precision</span></td><td><span style="color:rgb(0,0,0);">整型或浮点型</span></td><td><span style="color:rgb(0,0,0);">否</span></td><td><span style="color:rgb(0,0,0);">1</span></td><td><span style="color:rgb(0,0,0);">得分精度，取值范围(0,1]，影响各维度分数</span></td></tr><tr><td><span style="color:rgb(0,0,0);">customized_lexicon</span></td><td><span style="color:rgb(0,0,0);">对象</span></td><td><span style="color:rgb(0,0,0);">否</span></td><td>&nbsp;</td><td><p><span style="color:rgb(0,0,0);">指定特定单词期望发音，使用 CMU音素符号，单词、CMU大小写不敏感。支持多单词、多音标</span></p><p><span style="color:rgb(0,0,0);">如:</span></p><p><span style="color:rgb(0,0,0);">"customized_lexicon":</span></p><p><span style="color:rgb(0,0,0);">{"but": [</span></p><p><span style="color:rgb(0,0,0);">&nbsp; &nbsp; &nbsp; ["B", "IY","Y","UW","T","IY"],</span></p><p><span style="color:rgb(0,0,0);">&nbsp; &nbsp; &nbsp; ["B","UH","T"]</span></p><p><span style="color:rgb(0,0,0);">]}</span></p></td></tr><tr><td><span style="color:rgb(0,0,0);">customized_pron</span></td><td><span style="color:rgb(0,0,0);">对象</span></td><td><span style="color:rgb(0,0,0);">否</span></td><td>&nbsp;</td><td><p><span style="color:rgb(0,0,0);">指定单词期望发音，可选 KK/IPA88，支持多单词、多音标，音标不需要//括起来，单词、CMU大小写不敏感。支持多单词、多音标</span></p><p><span style="color:rgb(0,0,0);">示例：</span></p><p><span style="color:rgb(0,0,0);">"customized_pron":{</span></p><p><span style="color:rgb(0,0,0);">&nbsp; &nbsp; &nbsp; &nbsp; "type": "KK",</span></p><p><span style="color:rgb(0,0,0);">&nbsp; &nbsp; &nbsp; &nbsp; "pron":{"but":[</span></p><p><span style="color:rgb(0,0,0);">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;"bət","bʌt"</span></p><p><span style="color:rgb(0,0,0);">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; ]}</span></p><p><span style="color:rgb(0,0,0);">}</span></p></td></tr><tr><td><span style="color:rgb(0,0,0);">realtime_feedback</span></td><td><span style="color:rgb(0,0,0);">整型</span></td><td><span style="color:rgb(0,0,0);">否</span></td><td><span style="color:rgb(0,0,0);">0</span></td><td><span style="color:rgb(0,0,0);">支持评测结果实时反馈。</span><span style="color:hsl(0,0%,0%);">可选值0、1，</span><span style="color:rgb(0,0,0);">0为关闭，1为打开</span></td></tr><tr><td>output_rawtext</td><td><span style="color:hsl(0,0%,0%);">整型</span></td><td><span style="color:hsl(0,0%,0%);">否</span></td><td><span style="color:hsl(0,0%,0%);">0</span></td><td><span style="color:hsl(0,0%,0%);">保留标点符号。可选值0、1，</span><span style="color:rgb(0,0,0);">0为关闭、1为开启</span></td></tr><tr><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;">readtype_diagnosis</td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;">整型</td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;"><span style="color:rgb(0,0,0);">否</span></td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;">0</td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;"><p><span style="color:rgb(0,0,0);">显示重复读漏读。可选值 0、1，0为关闭、1为开启</span></p><p><span style="color:rgb(230,76,76);">注：</span></p><p><span style="color:rgb(230,76,76);">1. readtype_diagnosis设置为1时 ，通过result.sentences[].details[].readType字段对每个单词朗读情况进行了标识。</span></p><p><span style="color:rgb(230,76,76);">2. readtype_diagnosis设置为1时，重复读、错读时result.sentences[].details[]数组内单词个数可能大于refText评测文本单词数。</span></p></td></tr><tr><td>enable_digit_by_digit_read</td><td><span style="color:rgb(0,0,0);">整型</span></td><td>否</td><td>0</td><td><p>允许数字拆分成单个数字读，默认为0，可选值：0、1，0表示关闭，1表示开启</p><p>示例：1234 5678 001 &nbsp;可以按照1 2 3 4 5 6 7 8 0 0 1去读</p></td></tr></tbody></table></figure><p>&nbsp;</p><h2><strong>结果示例（有errId返回结果）</strong></h2><pre><code class="language-plaintext">{
    "applicationId": "xxx",
    "dtLastResponse": "2025-06-10 10:03:15:496",
    "eof": 1,
    "errId": xx,
    "error": "xx",
    "params": {
        "app": {
            "applicationId": "xxx",
            "sig": "xxx",
            "timestamp": "1749520994",
            "userId": "xxx"
        },
        "audio": {
            "audioType": "wav",
            "channel": 1,
            "sampleBytes": 2,
            "sampleRate": 16000
        },
        "coreProvideType": "cloud",
        "request": {
            "coreType": "para.eval",
            "phoneme_output": 1,
            "refText": "你",
            "tokenId": "xxx"
        }
    },
    "recordId": "xxx",      // 注：建议业务层保存，方便排查错误
    "refText": "你",
    "tokenId": "xx"
}
</code></pre><h2><strong>中间结果示例（realtime_feedback 设置开启）</strong></h2><p><span style="color:hsl(0,75%,60%);"><strong>注：全量返回用户实际朗读内容（只显示评测文本里的内容）</strong></span></p><p><span style="background-color:rgb(255,255,255);color:rgb(230,77,77);">&nbsp; &nbsp; &nbsp; &nbsp;<strong>当返回结果出现errId字段时，无中间结果</strong></span></p><pre><code class="language-plaintext">{
    "eof": 0,                                              //0：返回中间结果；1：返回最终结果
    "recordId": "xxx",                                     //评分唯一 id（同最终结果 recordId） 注：建议业务层保存，方便排查错误
    "timestamp": "2021-01-21 09:00:41:274",
    "result": {                                            //评分结果
        "kernel_version": "4.9.6",                               
        "pronunciation": 33,
        "resource_version": "2.8.8",
        "sentences": [{
                "sentence": "Don't say you love is who,",
                "details": [{							   //需开启paragraph_need_word_score
                        "skipped": 0,                      //是否已读，0表示已读，1表示未读
                        "word": "Don't",
                        "overall": 68,                     //单词总分
                        "charType": 0,
                        "word_parts": [{                   //单词文本信息
                                "part": "Don't",
                                "endIndex": 4,             
                                "beginIndex": 0,           
                                "charType": 0
                            }
                        ]
                    }, {
                        "skipped": 1,
                        "word": "say",
                        "overall": 0,
                        "charType": 0,
                        "word_parts": [{
                                "part": "say",
                                "endIndex": 8,
                                "beginIndex": 6,
                                "charType": 0
                            }
                        ]
                    }, {
                        "skipped": 1,
                        "word": "you",
                        "overall": 0,
                        "charType": 0，
                         "word_parts": [{
                                "part": "you",
                                "endIndex": 12,
                                "beginIndex": 10,
                                "charType": 0
                            }
                        ]
                    }, {
                        "skipped": 1,
                        "word": "love",
                        "overall": 0,
                        "charType": 0,
                        "word_parts": [{
                                "part": "love",
                                "endIndex": 17,
                                "beginIndex": 14,
                                "charType": 0
                            }
                        ]
                    }, {
                        "skipped": 1,
                        "word": "is",
                        "overall": 0,
                        "charType": 0,
                        "word_parts": [{
                                "part": "is",
                                "endIndex": 20,
                                "beginIndex": 19,
                                "charType": 0
                            }
                        ]
                    }, {
                        "skipped": 1,
                        "word": "who,",
                        "overall": 0,
                        "charType": 0,
                        "word_parts": [{
                                "part": "who",
                                "endIndex": 24,
                                "beginIndex": 22,
                                "charType": 0
                            }, {
                                "part": ",",
                                "endIndex": 25,
                                "beginIndex": 25,
                                "charType": 1
                            }
                        ]
                    }
                ],
                "overall": 11
            }, {
                "sentence": "life is still very long,",
                "details": [{
                        "skipped": 1,
                        "word": "life",
                        "overall": 0,
                        "charType": 0,
                        "word_parts": [{
                                "part": "life",
                                "endIndex": 26,
                                "beginIndex": 29,
                                "charType": 0
                            }
                        ]
                    }, {
                        "skipped": 1,
                        "word": "is",
                        "overall": 0,
                        "charType": 0,
                        "word_parts": [{
                                "part": "is",
                                "endIndex": 26,
                                "beginIndex": 29,
                                "charType": 0
                            }
                        ]
                    }, {
                        "skipped": 1,
                        "word": "still",
                        "overall": 0,
                        "charType": 0,
                        "word_parts": [{
                                "part": "still",
                                "endIndex": 38,
                                "beginIndex": 34,
                                "charType": 0
                            }
                        ]
                    }, {
                        "skipped": 1,
                        "word": "very",
                        "overall": 0,
                        "charType": 0,
                        "word_parts": [{
                                "part": "very",
                                "endIndex": 43,
                                "beginIndex": 40,
                                "charType": 0
                            }
                        ]
                    }, {
                        "skipped": 1,
                        "word": "long,",
                        "overall": 0,
                        "charType": 0,
                        "word_parts": [{
                                "part": "long",
                                "endIndex": 48,
                                "beginIndex": 45,
                                "charType": 0
                            }, {
                                "part": ",",
                                "endIndex": 49,
                                "beginIndex": 49,
                                "charType": 1
                            }
                        ]
                    }
                ],
                "overall": 0
            }, {
                "sentence": "can not predict tomorrow.",
                "details": [{
                        "skipped": 1,
                        "word": "can",
                        "overall": 0,
                        "charType": 0,
                        "word_parts": [{
                                "endIndex": 52,
                                "beginIndex": 50,
                                "charType": 0,
                                "part": "can"
                            }
                        ]
                    }, {
                        "skipped": 0,
                        "word": "not",
                        "overall": 64,
                        "charType": 0,
                        "word_parts": [{
                                "endIndex": 56,
                                "beginIndex": 54,
                                "charType": 0,
                                "part": "not"
                            }
                        ]
                    }, {
                        "skipped": 0,
                        "word": "predict",
                        "overall": 0,
                        "charType": 0,
                        "word_parts": [{
                                "endIndex": 64,
                                "beginIndex": 58,
                                "charType": 0,
                                "part": "predict"
                            }
                        ]
                    }, {
                        "skipped": 0,
                        "word": "tomorrow.",
                        "overall": 0,
                        "charType": 0,
                        "word_parts": [{
                                "endIndex": 73,
                                "beginIndex": 66,
                                "charType": 0,
                                "part": "tomorrow"
                            }, {
                                "endIndex": 74,
                                "beginIndex": 74,
                                "charType": 1,
                                "part": "."
                            }
                        ]

                    }
                ],
                "overall": 16
            }
        ],
        "overall": 9
    }
}</code></pre><h3>&nbsp;</h3><h2><strong>中间结果字段说明（中间结果 realtime_feedback &nbsp;设置开启后返回）</strong></h2><p><span style="background-color:rgb(255,255,255);color:rgb(230,77,77);"><strong>注：当返回结果出现errId字段时，无中间结果</strong></span></p><figure class="table"><table><tbody><tr><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>属性</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>类型</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>含义</strong></span></td></tr><tr><td><span style="color:rgb(0,0,0);">timestamp &nbsp;</span></td><td><span style="color:rgb(0,0,0);">字符串</span></td><td>评分结果返回时间</td></tr><tr><td><span style="color:rgb(0,0,0);">eof</span></td><td><span style="color:rgb(0,0,0);">整型</span></td><td><span style="color:rgb(0,0,0);">0：中间结果；1：最终结果</span></td></tr><tr><td><span style="color:rgb(0,0,0);">recordId</span></td><td><span style="color:rgb(0,0,0);">字符串</span></td><td><span style="color:rgb(0,0,0);">评分唯一 id（同最终结果 recordId） </span><span style="color:hsl(0,75%,60%);">注：建议业务层保存，方便排查错误</span></td></tr><tr><td><span style="color:rgb(0,0,0);">result</span></td><td><span style="color:rgb(0,0,0);">对象</span></td><td><span style="color:rgb(0,0,0);">评分结果</span></td></tr></tbody></table></figure><p>&nbsp;</p><h3><span style="color:rgb(0,0,0);"><strong>result</strong></span></h3><figure class="table"><table><tbody><tr><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>属性</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>类型</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>含义</strong></span></td></tr><tr><td><span style="color:rgb(0,0,0);">overall</span></td><td><span style="color:rgb(0,0,0);">整型或浮点型</span></td><td><span style="color:rgb(0,0,0);">句子总分。</span><span style="color:rgb(230,76,76);">若 precision（精度）设置为浮点型时，该值显示为浮点型（若该值小数 点后为 0，则显示成整型），若 scale（分 制）设置为（0,1），该值显示为浮点型， 否则为整型。</span></td></tr><tr><td><span style="color:rgb(0,0,0);">resource_version</span></td><td>字符串</td><td>资源版本</td></tr><tr><td><span style="color:rgb(0,0,0);">sentences</span></td><td>对象数组</td><td>句子评分结果</td></tr><tr><td><span style="color:rgb(0,0,0);">pronunciation</span></td><td><span style="color:rgb(0,0,0);">整型或浮点型</span></td><td><span style="color:rgb(0,0,0);">发音得分。</span><span style="color:rgb(230,76,76);">若 precision（精度）设置为浮点型时，该值显示为浮点型（若该值小数 点后为 0，则显示成整型），若 scale（分 制）设置为（0,1），该值显示为浮点型， 否则为整型。</span></td></tr><tr><td>kernel_version</td><td>字符串</td><td><span style="color:rgb(0,0,0);">内核版本</span></td></tr></tbody></table></figure><p>&nbsp;</p><h4><span style="color:rgb(0,0,0);"><strong>result- &gt;&nbsp;sentences（对象数组）</strong></span></h4><figure class="table"><table><tbody><tr><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>属性</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>类型</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>含义</strong></span></td></tr><tr><td><span style="color:rgb(0,0,0);">sentence</span></td><td><span style="color:rgb(0,0,0);">整型</span></td><td>句子文本</td></tr><tr><td><span style="color:rgb(0,0,0);">overall</span></td><td><span style="color:rgb(0,0,0);">整型或浮点型</span></td><td>句子总分。<span style="color:rgb(230,76,76);">若 precision（精度）设置为浮点型时，该值显示为浮点型（若该值小数 点后为 0，则显示成整型），若 scale（分 制）设置为（0,1），该值显示为浮点型， 否则为整型。</span></td></tr><tr><td><span style="color:rgb(0,0,0);">details</span></td><td><span style="color:rgb(0,0,0);">对象数组</span></td><td><span style="color:hsl(0,0%,0%);">句子评分详情。</span><span style="color:hsl(0,75%,60%);">需开启paragraph_need_word_score</span></td></tr></tbody></table></figure><p>&nbsp;</p><p><span style="color:rgb(0,0,0);"><strong>result- &gt;&nbsp;sentences- &gt;details</strong></span></p><figure class="table"><table><tbody><tr><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>属性</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>类型</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>含义</strong></span></td></tr><tr><td><span style="color:rgb(0,0,0);">charType&nbsp;</span></td><td><span style="color:rgb(0,0,0);">整型</span></td><td><span style="color:rgb(0,0,0);">0：非标点符号，1：标点符号</span></td></tr><tr><td><span style="color:rgb(0,0,0);">overall</span></td><td>整型或浮点型</td><td>单词分数，<span style="color:rgb(230,76,76);">若 precision（精度）设置为浮点型时，该值显示为浮点型（若该值小数 点后为 0，则显示成整型），若 scale（分 制）设置为（0,1），该值显示为浮点型， 否则为整型。</span></td></tr><tr><td><span style="color:rgb(0,0,0);">word</span></td><td><span style="color:rgb(0,0,0);">字符串</span></td><td><span style="color:rgb(0,0,0);">单词</span></td></tr><tr><td><span style="color:rgb(0,0,0);">skipped</span></td><td><span style="color:rgb(0,0,0);">整型</span></td><td>是否已读，0表示已读，1表示未读 &nbsp;&nbsp;</td></tr></tbody></table></figure><p>&nbsp;</p><p><span style="color:rgb(0,0,0);"><strong>result- &gt;&nbsp;sentences- &gt;details- &gt;&nbsp;word_parts</strong></span></p><figure class="table"><table><tbody><tr><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>属性</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>类型</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);">含义</span></td></tr><tr><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;"><span style="color:rgb(0,0,0);">charType&nbsp;</span></td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;">整型</td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;"><span style="color:rgb(0,0,0);">0：非标点符号，1：标点符号</span></td></tr><tr><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;"><span style="color:rgb(0,0,0);">part&nbsp;</span></td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;"><span style="color:rgb(0,0,0);">字符串</span></td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;"><span style="color:rgb(0,0,0);">文本</span></td></tr><tr><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;">endIndex</td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;">整型</td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;">单词在文本中结束的位置</td></tr><tr><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;">beginIndex</td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;">整型</td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;">单词在文本中开始的位置</td></tr></tbody></table></figure><hr><h2><strong>最终结果示例</strong></h2><pre><code class="language-plaintext">{
  "recordId": "xxxx",                      //注：建议业务层保存，方便排查错误
  "eof": 1,
  "applicationId": "xxx",
  "result": {
    "pronunciation": 54,
    "speed": 109,
    "kernel_version": "6.4.4",
    "integrity": 60,
    "resource_version": "4.0.5",
	"warning": [
      {
        "code": 1004,
        "message": "Audio noisy!"
      }
    ],
    "sentences": [
      {
        "sentence": "It is an old book",
        "start": 70,
        "details": [                            //需开启paragraph_need_word_score
          {
            "charType": 0,
            "readType": 0,
            "start": 70,
            "prominence": 0,
            "word_parts": [
              {
                "part": "It",
                "endIndex": 1,
                "charType": 0,
                "beginIndex": 0
              }
            ],
            "end": 96,
            "word": "It",
            "overall": 100
          },
          {
            "charType": 0,
            "readType": 0,
            "start": 96,
            "prominence": 0,
            "word_parts": [
              {
                "part": "is",
                "endIndex": 4,
                "charType": 0,
                "beginIndex": 3
              }
            ],
            "end": 150,
            "word": "is",
            "overall": 89
          },
          {
            "scores": {
              "prominence": 0,
              "pronunciation": 51,
              "overall": 51
            },
            "readType": 4,
            "charType": 0,
            "word": "old"
          },
          {
            "charType": 0,
            "readType": 3,
            "start": 184,
            "prominence": 0,
            "word_parts": [
              {
                "part": "an",
                "endIndex": 7,
                "charType": 0,
                "beginIndex": 6
              }
            ],
            "end": 199,
            "word": "an",
            "overall": 0
          },
          {
            "charType": 0,
            "readType": 0,
            "start": 271,
            "prominence": 0,
            "word_parts": [
              {
                "part": "old",
                "endIndex": 11,
                "charType": 0,
                "beginIndex": 9
              }
            ],
            "end": 315,
            "word": "old",
            "overall": 28
          },
          {
            "charType": 0,
            "readType": 3,
            "start": 336,
            "prominence": 0,
            "word_parts": [
              {
                "part": "book",
                "endIndex": 16,
                "charType": 0,
                "beginIndex": 13
              }
            ],
            "end": 345,
            "word": "book",
            "overall": 0
          }
        ],
        "overall": 43,
        "endIndex": 16,
        "end": 345,
        "beginIndex": 0
      }
    ],
    "rhythm": 56,
    "overall": 33,
    "fluency": 54,
    "duration": "3.528",
    "numeric_duration": 3.528
  },
  "tokenId": "abcd",
  "refText": "It is an old book",
  "dtLastResponse": "2024-04-23 11:39:03:522",
  "params": {
    "app": {
      "userId": "user_id",
      "timestamp": "1713843541",
      "applicationId": "xxxx",
      "sig": "df611c6e31b44d7a89e5a0299272186e2859cbcb"
    },
    "request": {
      "paragraph_need_word_score": 1,
      "tokenId": "abcd",
      "coreType": "para.eval",
      "readtype_diagnosis": 1,
      "refText": "It is an old book"
    },
    "audio": {
      "audioType": "mp3",
      "sampleBytes": 2,
      "sampleRate": 16000,
      "channel": 1
    }
  }
}</code></pre><h3>&nbsp;</h3><h2><span style="color:rgb(0,0,0);"><strong>最终结果字段说明</strong></span></h2><figure class="table"><table><tbody><tr><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>属性</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>类型</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>含义</strong></span></td></tr><tr><td><span style="color:rgb(0,0,0);">tokenId</span></td><td><span style="color:rgb(0,0,0);">字符串</span></td><td><span style="color:rgb(0,0,0);">终端用户请求 id</span></td></tr><tr><td><span style="color:rgb(0,0,0);">refText</span></td><td><span style="color:rgb(0,0,0);">字符串</span></td><td><span style="color:rgb(0,0,0);">参考信息</span></td></tr><tr><td><span style="color:rgb(0,0,0);">audioUrl</span></td><td><span style="color:rgb(0,0,0);">字符串</span></td><td><span style="color:rgb(0,0,0);">在线音频地址，</span><span style="color:hsl(0,75%,60%);">需设置attachAudioUrl</span></td></tr><tr><td><span style="color:rgb(0,0,0);">dtLastResponse</span></td><td><span style="color:rgb(0,0,0);">字符串</span></td><td><span style="color:rgb(0,0,0);">评分结果返回时间</span></td></tr><tr><td><span style="color:rgb(0,0,0);">result</span></td><td>对象</td><td><span style="color:rgb(0,0,0);">评分结果</span></td></tr><tr><td><span style="color:rgb(0,0,0);">applicationId</span></td><td><span style="color:rgb(0,0,0);">字符串</span></td><td><span style="color:rgb(0,0,0);">appKey</span></td></tr><tr><td><span style="color:rgb(0,0,0);">recordId</span></td><td><span style="color:rgb(0,0,0);">字符串</span></td><td><span style="color:rgb(0,0,0);">评分唯一id &nbsp;</span><span style="color:hsl(0,75%,60%);">注：建议业务层保存，方便排查错误</span></td></tr><tr><td><span style="color:rgb(0,0,0);">params</span></td><td><span style="color:rgb(0,0,0);">对象</span></td><td><span style="color:rgb(0,0,0);">请求参数，</span><span style="color:hsl(0,75%,60%);">需设置getParam</span></td></tr><tr><td>eof</td><td>整型</td><td>0：中间结果；1：最终结果</td></tr><tr><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;">errId</td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;"><span style="color:rgb(0,0,0);">整型</span></td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;">错误码 &nbsp; &nbsp;<span style="color:rgb(230,77,77);">注：当出现该字段时，返回结果无result字段</span></td></tr><tr><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;">error</td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;"><span style="color:rgb(0,0,0);">字符串</span></td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;">错误码信息 &nbsp;&nbsp;<span style="color:rgb(230,77,77);"> 注：当出现该字段时，返回结果无result字段</span></td></tr></tbody></table></figure><h3><span style="color:rgb(0,0,0);"><strong>result</strong></span></h3><p><span style="background-color:rgb(255,255,255);color:rgb(230,77,77);">注：当返回结果出现errId字段时，无该字段</span></p><figure class="table"><table><tbody><tr><td style="text-align:center;"><strong>属性</strong></td><td style="text-align:center;"><strong>类型</strong></td><td style="text-align:center;"><strong>含义</strong></td></tr><tr><td><span style="color:rgb(0,0,0);">fluency</span></td><td>整型或浮点型</td><td><span style="color:rgb(0,0,0);">流利度。</span><span style="color:rgb(230,76,76);">若 precision（精度）设置为浮点型时，该值显示为浮点型（若该值小数 点后为 0，则显示成整型），若 scale（分 制）设置为（0,1），该值显示为浮点型， 否则为整型。</span></td></tr><tr><td><span style="color:rgb(0,0,0);">duration</span></td><td><span style="color:rgb(0,0,0);">字符串</span></td><td><span style="color:rgb(0,0,0);">音频时长。单位：秒</span></td></tr><tr><td><span style="color:rgb(0,0,0);">kernel_version</span></td><td><span style="color:rgb(51,51,51);">字符串</span></td><td><span style="color:rgb(0,0,0);">内核版本</span></td></tr><tr><td><span style="color:rgb(0,0,0);">speed</span></td><td>整型</td><td><span style="color:rgb(0,0,0);">语速。单位：词/分</span></td></tr><tr><td><span style="color:rgb(0,0,0);">integrity</span></td><td><span style="color:rgb(0,0,0);">整型或浮点型</span></td><td><span style="color:rgb(0,0,0);">完整度。</span><span style="color:rgb(230,76,76);">若 precision（精度）设置为浮点型时，该值显示为浮点型（若该值小数 点后为 0,则显示成整型），若 scale（分 制）设置为（0,1）, 该值显示为浮点型， 否则为整型。</span></td></tr><tr><td><span style="color:rgb(0,0,0);">sentences</span></td><td><span style="color:rgb(0,0,0);">对象数组</span></td><td><span style="color:rgb(0,0,0);">段落详情</span></td></tr><tr><td><span style="color:rgb(0,0,0);">pronunciation</span></td><td><span style="color:rgb(0,0,0);">整型或浮点型</span></td><td><span style="color:rgb(0,0,0);">发音得分。</span><span style="color:rgb(230,76,76);">若 precision（精度）设置为浮点型时，该值显示为浮点型（若该值小数 点后为 0，则显示成整型），若 scale（分 制）设置为（0,1），该值显示为浮点型， 否则为整型。</span></td></tr><tr><td><span style="color:rgb(0,0,0);">overall</span></td><td><span style="color:rgb(0,0,0);">整型或浮点型</span></td><td><span style="color:rgb(0,0,0);">总分。</span><span style="color:rgb(230,76,76);">若 precision（精度）设置为浮点型时，该值显示为浮点型（若该值小数 点后为 0,则显示成整型），若 scale（分 制）设置为（0,1）, 该值显示为浮点型， 否则为整型。</span></td></tr><tr><td><span style="color:rgb(0,0,0);">resource_version</span></td><td><span style="color:rgb(51,51,51);">字符串</span></td><td><span style="color:rgb(0,0,0);">资源版本</span></td></tr><tr><td><span style="color:rgb(51,51,51);">rhythm</span></td><td><span style="color:rgb(0,0,0);">整型或浮点型</span></td><td><span style="color:rgb(0,0,0);">韵律度得分。</span><span style="color:rgb(230,76,76);">若 precision（精度）设置为浮点型时，该值显示为浮点型（若该值小数 点后为 0，则显示成整型），若 scale（分 制）设置为（0,1），该值显示为浮点型， 否则为整型。</span></td></tr><tr><td><span style="color:rgb(0,0,0);">warning</span></td><td><span style="color:rgb(0,0,0);">对象数组</span></td><td>音频检测的提示。<span style="color:rgb(230,76,76);"> &nbsp;注:非必现字段，使用前需先判断该字段是否存在，详情请参考：</span><a href="/docs/index?id=25" target="_blank"><span style="color:rgb(230,76,76);">服务端错误码</span></a></td></tr><tr><td>numeric_duration</td><td>整型或浮点型</td><td>音频时长。单位：秒</td></tr></tbody></table></figure><p>&nbsp;</p><h4><span style="color:rgb(0,0,0);"><strong>result-&gt;warning（对象数组）</strong></span></h4><figure class="table"><table><tbody><tr><td style="text-align:center;"><strong>属性</strong></td><td style="text-align:center;"><strong>类型</strong></td><td style="text-align:center;"><strong>含义</strong></td></tr><tr><td>code&nbsp;</td><td>数字&nbsp;</td><td>错误码</td></tr><tr><td>message</td><td>字符串</td><td>音频检测的提示信息，具体错误提示请参考“<a href="/docs/index?id=25" target="_blank">服务端返回错误码</a>“</td></tr></tbody></table></figure><p>&nbsp;</p><h4><span style="color:rgb(0,0,0);"><strong>result-&gt;sentences（对象数组）</strong></span></h4><figure class="table"><table><tbody><tr><td style="text-align:center;"><strong>属性</strong></td><td style="text-align:center;"><strong>类型</strong></td><td style="text-align:center;"><strong>含义</strong></td></tr><tr><td><span style="color:rgb(0,0,0);">overall</span></td><td><span style="color:rgb(0,0,0);">整型或浮点型</span></td><td><span style="color:rgb(0,0,0);">句子总分。</span><span style="color:rgb(230,76,76);">若 precision（精度）设置为浮点型时，该值显示为浮点型（若该值小数 点后为 0，则显示成整型），若 scale（分 制）设置为（0,1），该值显示为浮点型， 否则为整型。</span></td></tr><tr><td><span style="color:rgb(0,0,0);">end</span></td><td>整型</td><td><span style="color:rgb(0,0,0);">句子结束在音轨上的时间，单位：10毫秒</span></td></tr><tr><td><span style="color:rgb(0,0,0);">details</span></td><td><span style="color:rgb(0,0,0);">对象数组</span></td><td><span style="color:rgb(0,0,0);">句子中单词的详细信息，</span><span style="color:hsl(0,75%,60%);">需要开启paragraph_need_word_score</span></td></tr><tr><td><span style="color:rgb(0,0,0);">sentence</span></td><td><span style="color:rgb(0,0,0);">字符串</span></td><td><span style="color:rgb(0,0,0);">句子内容</span></td></tr><tr><td><span style="color:rgb(0,0,0);">start</span></td><td>整型</td><td><span style="color:rgb(0,0,0);">句子开始在音轨上的时间，单位：10毫秒</span></td></tr><tr><td>beginIndex</td><td>整型</td><td>句子在文本中开始的位置</td></tr><tr><td>endIndex</td><td>整型</td><td>句子在文本中结束的位置</td></tr></tbody></table></figure><p>&nbsp;</p><p><span style="color:rgb(0,0,0);"><strong>result-&gt;sentences-&gt;details</strong></span></p><p><span style="color:hsl(0,75%,60%);"><strong>一、当存在readType字段且值为4时，结构如下：</strong></span></p><figure class="table"><table><tbody><tr><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>属性</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>类型</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>含义</strong></span></td></tr><tr><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;"><span style="color:rgb(0,0,0);">scores</span></td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;"><span style="color:rgb(0,0,0);">对象</span></td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;"><span style="color:rgb(0,0,0);">句子中的单词得分情况。</span><span style="color:rgb(230,76,76);">若 precision（精度）设置为浮点型时，该值显示为浮点型（若该值小数 点后为 0，则显示成整型），若 scale（分 制）设置为（0,1），该值显示为浮点型， 否则为整型。</span></td></tr><tr><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;"><span style="color:rgb(0,0,0);">charType</span></td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;">整型</td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;"><span style="color:rgb(51,51,51);">0：非标点符号，1：标点符号。 &nbsp;&nbsp;</span><span style="color:rgb(0,0,0);"> </span><span style="color:rgb(230,77,77);">注: charType值为1时，words 只显示charType跟word字段</span></td></tr><tr><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;">readType</td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;">整型</td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;"><span style="color:rgb(0,0,0);">4为重复读。 </span><span style="color:rgb(230,77,77);">注：需开启readtype_diagnosis</span></td></tr><tr><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;">word</td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;"><span style="color:rgb(51,51,51);">字符串</span></td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;">评测文本中的单词</td></tr></tbody></table></figure><p>&nbsp;</p><p><span style="color:rgb(0,0,0);"><strong>result- &gt;sentences-&gt;details- &gt;scores</strong></span></p><figure class="table"><table><tbody><tr><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>属性</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>类型</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>含义</strong></span></td></tr><tr><td><span style="color:rgb(0,0,0);">prominence</span></td><td>整型</td><td><span style="color:rgb(0,0,0);">单词重(zhòng)读， </span><span style="color:rgb(51,51,51);">0表示非重读、 1表示重读</span></td></tr><tr><td><span style="color:rgb(0,0,0);">overall</span></td><td>整型或浮点型</td><td><span style="color:rgb(0,0,0);">句子中的单词总分。</span><span style="color:rgb(230,76,76);">若 precision（精度）设置为浮点型时，该值显示为浮点型（若该值小数 点后为 0，则显示成整型），若 scale（分 制）设置为（0,1），该值显示为浮点型， 否则为整型。</span></td></tr><tr><td><span style="color:rgb(0,0,0);">pronunciation</span></td><td><span style="color:rgb(0,0,0);">整型或浮点型</span></td><td><span style="color:rgb(0,0,0);">句子中的单词发音得分</span><span style="color:rgb(57,57,57);">，</span><span style="color:rgb(230,76,76);">若 precision（精度）设置为浮点型时，该值显示为浮点型（若该值小数 点后为 0，则显示成整型），若 scale（分 制）设置为（0,1），该值显示为浮点型， 否则为整型。</span></td></tr></tbody></table></figure><p>&nbsp;</p><p><span style="color:hsl(0,75%,60%);"><strong>二、其他情况，结构如下：</strong></span></p><figure class="table"><table><tbody><tr><td style="text-align:center;"><strong>属性</strong></td><td style="text-align:center;"><strong>类型</strong></td><td style="text-align:center;"><strong>含义</strong></td></tr><tr><td><span style="color:rgb(0,0,0);">prominence</span></td><td>整型</td><td><span style="color:rgb(0,0,0);">句子中单词重(zhòng)读，</span><span style="color:rgb(51,51,51);">0 表示非重读、 1 表示重读&nbsp;</span></td></tr><tr><td><span style="color:rgb(0,0,0);">charType</span></td><td>整型</td><td><span style="color:rgb(51,51,51);">0：非标点符号，1：标点符号</span></td></tr><tr><td><span style="color:rgb(0,0,0);">overall</span></td><td><span style="color:rgb(0,0,0);">整型或浮点型</span></td><td><span style="color:rgb(0,0,0);">句子单词的总分。</span><span style="color:rgb(230,76,76);">若 precision（精度）设置为浮点型时，该值显示为浮点型（若该值小数 点后为 0，则显示成整型），若 scale（分 制）设置为（0,1），该值显示为浮点型， 否则为整型。</span></td></tr><tr><td><span style="color:rgb(0,0,0);">word</span></td><td><span style="color:rgb(0,0,0);">字符串</span></td><td><span style="color:rgb(0,0,0);">句子中的单词</span></td></tr><tr><td><span style="color:rgb(0,0,0);">start</span></td><td>整型</td><td><span style="color:rgb(0,0,0);">句子中单词开始在音轨上的时间 ，单位：10毫秒</span></td></tr><tr><td><span style="color:rgb(0,0,0);">end</span></td><td><span style="color:rgb(0,0,0);">整型</span></td><td><span style="color:rgb(0,0,0);">句子中单词结束在音轨上的时间 &nbsp;，单位：10毫秒</span></td></tr><tr><td>word_parts</td><td><span style="color:rgb(0,0,0);">对象数组</span></td><td>单词信息&nbsp;</td></tr><tr><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;">readType</td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;">整型</td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;">0为正常，3为漏读&nbsp;<span style="color:rgb(0,0,0);"> &nbsp;</span><span style="color:rgb(230,77,77);">注：需开启readtype_diagnosis，才会显示该字段</span></td></tr></tbody></table></figure><p>&nbsp;</p><p><span style="color:rgb(0,0,0);"><strong>result-&gt;sentences-&gt;details-&gt;word_parts&nbsp;</strong></span></p><figure class="table"><table><tbody><tr><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>属性</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>类型</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>含义</strong></span></td></tr><tr><td>beginIndex</td><td>整型</td><td>单词在文本中开始的位置</td></tr><tr><td>endIndex</td><td>整型</td><td>单词在文本中结束的位置</td></tr><tr><td><span style="color:rgb(0,0,0);">charType</span></td><td>整型</td><td><span style="color:rgb(51,51,51);">0：非标点符号，1：标点符号</span></td></tr><tr><td>part</td><td>字符串</td><td>单词</td></tr></tbody></table></figure>

                        <!-- <div id="editor" style="height:100%"></div> -->
                    </div>
                    <div id="catalog" class="col-md-2"></div>
                </div>
            </div>
        </div>
    </div>
</div>

                
			<div class="footer">
				<div class="footer-inner">
					<!-- #section:basics/footer -->
					<div class="footer-content">
						<span class="bigger-120">
							<span class="blue bolder">声通科技</span>
							&copy; 2025
						</span>
					</div>

					<!-- /section:basics/footer -->
				</div>
			</div>

			<a href="#" id="btn-scroll-up" class="btn-scroll-up btn btn-sm btn-inverse">
				<i class="ace-icon fa fa-angle-double-up icon-only bigger-110"></i>
			</a>
		</div><!-- /.main-container -->

		<!-- basic scripts -->

		<!--[if !IE]> -->
		<script src="/assets/js/jquery-2.2.4.min.js"></script>

		<!-- <![endif]-->

		<!--[if IE]>
        <script src="/components/jquery.1x/dist/jquery.js"></script>
        <![endif]-->
		<script type="text/javascript">
			if('ontouchstart' in document.documentElement) document.write("<script src='../components/_mod/jquery.mobile.custom/jquery.mobile.custom.js'>"+"<"+"/script>");
		</script>
		<script src="/components/bootstrap/dist/js/bootstrap.js"></script>

		<!-- page specific plugin scripts -->

		<!--[if lte IE 8]>
		  <script src="/components/ExplorerCanvas/excanvas.js"></script>
		<![endif]-->

        <!-- ace scripts -->
        
        <script src="/assets/js/ace-elements.min.js"></script>
		<script src="/assets/js/ace.min.js"></script>
		<script src="/components/jquery.gritter/js/jquery.gritter.min.js"></script>
		<script src="/components/bootbox/bootbox.all.min.js"></script>
		<script src="/assets/js/app.min.js"></script>
		<script src="/assets/js/search.min.js"></script>
		<!-- inline scripts related to this page -->
		<script type="text/JavaScript">
			jQuery(function($){
				//$(".nav-list .active").parent().parent().addClass("active open");

				$("a[data-action=edit-user]").on('click', function(){
					$this = $(this);
					var id = $this.data("id");
					var name = $this.data("name");
					var email = $this.data("email");
					dialog= bootbox.dialog({
						title: '编辑',
						buttons: {
							cancel: {
								label: "取消",
								className: 'btn-danger',
							},
							ok: {
								label: "确定",
								className: 'btn-info',
								callback: function(){
									$.ajax({
										type: "POST",
										url: "/users/edit_user",
										data: $("#user-form").serialize(),
										beforeSend: function() {
											var flag = true;
											$('#user-form input[required*=""]').each(function(){
												if($(this).val() == "") {
													$(this).focus();
													notice("","参数不能为空","warning");
													flag = false;
													return false;
												}
											});
											let password1 = $.trim($('input[name="password1"]').val());
											let password2 = $.trim($('input[name="password2"]').val());
											if ((password1 != '' || password2 != '') && password1!=password2) {
												notice("","两次输入密码不一致","warning");
												flag = false;
											}
											if(!flag) {
												return flag;
											}
											
										},
										success: function(data) {
											try {
												var obj = JSON.parse(data);
												if(obj.status == "ok") {
													$('.user-info span').text($('#user-form input[name=name]').val());
													$this.attr('data-name', $('#user-form input[name=name]').val());
													$this.attr('data-email', $('#user-form input[name=email]').val());
													notice("","保存成功","success");
													dialog.modal('hide');
												} else {
													notice("保存失败",obj.error,"error");
												}
											}catch(e) {
												notice("保存失败","","error");
											}
											
										},
										error: function(jqXHR, textStatus, errorThrown){
											console.log(jqXHR);
											console.log(textStatus);
											console.log(errorThrown);
											notice("保存失败",jqXHR.status + ':' + errorThrown,"error");
										},
										complete: function() {
											
										}
									})
									return false;
								}
							}
						},
						message: '<div class="row">'+
								'<div class="col-xs-12">'+
								'<form class="form-horizontal" id="user-form" role="form">' +
								'<div class="form-group">'+
								'<label class="col-sm-3 control-label no-padding-right required"> 昵称: </label>'+
								'<div class="col-sm-9">'+
								'<input type="text"  name="id" class="hidden" value="' + id + '"/>'+
								'<input type="text"  name="name" class="col-xs-10 col-sm-6" required value="' + name + '"/>'+
								'</div>'+
								'</div>'+
								'<div class="form-group">'+
								'<label class="col-sm-3 control-label no-padding-right required"> 邮箱: </label>'+
								'<div class="col-sm-9">'+
								'<input type="text"  name="email" class="col-xs-10 col-sm-6" required value="' + email + '"/>'+
								'</div>'+
								'</div>'+
								'<div class="form-group">'+
								'<label class="col-sm-3 control-label no-padding-right"> 密码: </label>'+
								'<div class="col-sm-9">'+
								'<input type="password" class="input-md" name="password1"/>&nbsp;'+
								'<input type="password" class="input-md" name="password2"/>'+
								'</div>'+
								'</div>'+
								'</form>'+
								'</div>'+
								'</div>',
					});
				});
			});
		</script>
		<script src="/assets/js/catalog.min.js"></script>
<script type="text/JavaScript">	
    jQuery(function($){
        doMenu($('#content'), $('#catalog'));
        $('table').addClass('table table-striped table-bordered table-hover');



            
        $('button[data-action="favourite"').on('click', function() {
            $this = $(this);
            let id = $(this).data('id');
            $.ajax({
                type: "POST",
                url: 'favourite',
                data: {id:id},
                beforeSend: function() {},
                success: function(data) {
                    try {
                        var obj = JSON.parse(data);
                        if(obj.status == "ok") {
                            let txt = $this.html();
                            if(txt=='已关注') {
                                $this.html('关注')
                                notice("","已取消关注","success");
                            } else {
                                $this.html('已关注');
                                if(obj.hasOwnProperty('error')) {
                                    notice("","已关注，" + obj.error,"warning");
                                }else{
                                    notice("","已关注","success");
                                }
                                
                            }
                        } else {
                            notice("操作失败",obj.error,"error");
                        }
                    }catch(e) {
                        console.log(e);
                        notice("操作失败","","error");
                    }
                    
                },
                error: function(jqXHR, textStatus, errorThrown){
                    console.log(jqXHR);
                    console.log(textStatus);
                    console.log(errorThrown);
                    notice("操作失败","","error");
                },
                complete: function() {
                }
            });
        })

    }); 
</script>

		
	</body>
</html>

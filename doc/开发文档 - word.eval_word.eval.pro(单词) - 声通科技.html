<!DOCTYPE html>
<html lang="en">
	<head>
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
		<meta charset="utf-8" />
		<title>开发文档 - word.eval&#47;word.eval.pro(单词)
 - 声通科技</title>

		<meta name="description" content="overview &amp; stats" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />

		<link rel="shortcut icon" href="/favicon.png">

		<!-- bootstrap & fontawesome -->
		<link rel="stylesheet" href="/assets/css/bootstrap.min.css" />
		<link rel="stylesheet" href="/components/font-awesome/css/font-awesome.css" />

		<!-- page specific plugin styles -->
		<link rel="stylesheet" href="/components/jquery.gritter/css/jquery.gritter.min.css" />

		<!-- text fonts -->
		<link rel="stylesheet" href="/assets/css/ace-fonts.min.css" />

		<!-- ace styles -->
		<link rel="stylesheet" href="/assets/css/ace.min.css" class="ace-main-stylesheet" id="main-ace-style" />

		<!--[if lte IE 9]>
			<link rel="stylesheet" href="assets/css/ace-part2.css" class="ace-main-stylesheet" />
		<![endif]-->
		<!--<link rel="stylesheet" href="/assets/css/ace-skins.css" />-->

		<!--[if lte IE 9]>
		  <link rel="stylesheet" href="/assets/css/ace-ie.css" />
		<![endif]-->

		<!-- inline styles related to this page -->


		<!-- HTML5shiv and Respond.js for IE8 to support HTML5 elements and media queries -->

		<!--[if lte IE 8]>
		<script src="/components/html5shiv/dist/html5shiv.min.js"></script>
		<script src="/components/respond/dest/respond.min.js"></script>
        <![endif]-->
        <link rel="stylesheet" href="/assets/css/app.css" />
        
	</head>

	<body class="no-skin">
		<!-- #section:basics/navbar.layout -->
		<div id="navbar" class="navbar navbar-default ace-save-state navbar-fixed-top">
			<div class="navbar-container ace-save-state" id="navbar-container">
				<!-- #section:basics/sidebar.mobile.toggle -->
				
				<button type="button" class="navbar-toggle menu-toggler pull-left" id="menu-toggler" data-target="#sidebar">
					<span class="sr-only">Toggle sidebar</span>

					<span class="icon-bar"></span>

					<span class="icon-bar"></span>

					<span class="icon-bar"></span>
				</button>
				

				<!-- /section:basics/sidebar.mobile.toggle -->
				<div class="navbar-header pull-left">
					<!-- #section:basics/navbar.layout.brand -->
					<a href="/" class="navbar-brand">
						<small>
							<!--<i class="fa fa-leaf"></i>-->
							声通科技
						</small>
					</a>
					<!-- /section:basics/navbar.layout.brand -->

					<!-- #section:basics/navbar.toggle -->

					<!-- /section:basics/navbar.toggle -->
				</div>

				<!-- #section:basics/navbar.dropdown -->
				<div class="navbar-buttons navbar-header pull-right" role="navigation">
					<ul class="nav ace-nav">
						<li class="light-blue li-nav-search">
							<div class="nav-search" id="nav-search" style="top:5px;">
								<form class="form-search">
									<span class="input-icon input-icon-right">
										<input type="text" placeholder="搜索" class="nav-search-input" style="width: 220px;height: 35px !important;color: #deebff !important;background: rgba(9,30,66,0.48);" data-action="nav-search-input" autocomplete="off">
										<i class="ace-icon fa fa-search nav-search-icon" style="top: 4px;"></i>
									</span>
								</form>
							</div>
						</li>
						
						<!-- #section:basics/navbar.user_menu -->
						<li class="light-blue dropdown-modal" style="float: right !important;">
							<a data-toggle="dropdown" href="#" class="dropdown-toggle">
								<!--<img class="nav-user-photo" src="/assets/avatars/user.jpg" alt="Jason's Photo" />-->
								<span class="user-info">
									<small>Welcome,</small>
									<span>seedtu</span>
								</span>
								<i class="ace-icon fa fa-caret-down"></i>
							</a>
							<ul class="user-menu dropdown-menu-right dropdown-menu dropdown-yellow dropdown-caret dropdown-close">
								<!-- <li>
									<a href="#">
										<i class="ace-icon fa fa-cog"></i>
										设置
									</a>
								</li> -->

								<li>
									<a data-id="634" data-name="seedtu" data-email="<EMAIL>"  data-action='edit-user'>
										<i class="ace-icon fa fa-user"></i>
										修改
									</a>
								</li>

								<li class="divider"></li>

								<li>
									<a href="/account/logout">
										<i class="ace-icon fa fa-power-off"></i>
										登出
									</a>
								</li>
							</ul>
						</li>

						<!-- /section:basics/navbar.user_menu -->
					</ul>
				</div>

				<!-- /section:basics/navbar.dropdown -->
			</div><!-- /.navbar-container -->
		</div>

		<!-- /section:basics/navbar.layout -->
		<div class="main-container ace-save-state" id="main-container">
			<script type="text/javascript">
				//try{ace.settings.loadState('main-container')}catch(e){}
			</script>

			<!-- #section:basics/sidebar -->
			<div id="sidebar" class="sidebar responsive ace-save-state sidebar-fixed sidebar-scroll">
				<ul class="nav nav-list" style="top: 0px;"><li class=""><a href="/index"><span class="menu-text"> 首页 </span></a><b class="arrow"></b></li><li class=""><a href="#" class="dropdown-toggle"><span class="menu-text">系统管理</span><b class="arrow fa fa-angle-down"></b></a><b class="arrow"></b><ul class="submenu"><li class=""><a href="/mail_manager/index"><span>关注邮箱管理</span></a><b class="arrow"></b></li></ul></li><li class="active open"><a href="#" class="dropdown-toggle"><span class="menu-text">文档中心</span><b class="arrow fa fa-angle-down"></b></a><b class="arrow"></b><ul class="submenu"><li class=""><a href="#" class="dropdown-toggle"><span>SDK文档</span><b class="arrow fa fa-angle-down"></b></a><b class="arrow"></b><ul class="submenu"><li class=""><a href="#" class="dropdown-toggle"><span>Flutter SDK</span><b class="arrow fa fa-angle-down"></b></a><b class="arrow"></b><ul class="submenu"><li class=""><a href="/docs/index?id=267"><span>接入指南</span></a><b class="arrow"></b></li><li class=""><a href="#" class="dropdown-toggle"><span>开发指南</span><b class="arrow fa fa-angle-down"></b></a><b class="arrow"></b><ul class="submenu"><li class=""><a href="/docs/index?id=268"><span>相关类说明</span></a><b class="arrow"></b></li><li class=""><a href="/docs/index?id=269"><span>FlutterPluginSTkouyu</span></a><b class="arrow"></b></li><li class=""><a href="/docs/index?id=271"><span>KYEngineSetting</span></a><b class="arrow"></b></li><li class=""><a href="/docs/index?id=274"><span>KYRecordSetting</span></a><b class="arrow"></b></li><li class=""><a href="/docs/index?id=276"><span>OnPlayListener</span></a><b class="arrow"></b></li><li class=""><a href="/docs/index?id=275"><span>OnInitEngineListener</span></a><b class="arrow"></b></li><li class=""><a href="/docs/index?id=277"><span>OnRecordListener</span></a><b class="arrow"></b></li><li class=""><a href="/docs/index?id=272"><span>KYEngineType</span></a><b class="arrow"></b></li><li class=""><a href="/docs/index?id=270"><span>KYAudioType</span></a><b class="arrow"></b></li><li class=""><a href="/docs/index?id=280"><span>KYEngineStatus</span></a><b class="arrow"></b></li></ul></li></ul></li></ul></li><li class="active open"><a href="#" class="dropdown-toggle"><span>评测参数及结果说明</span><b class="arrow fa fa-angle-down"></b></a><b class="arrow"></b><ul class="submenu"><li class="active open"><a href="#" class="dropdown-toggle"><span>英文</span><b class="arrow fa fa-angle-down"></b></a><b class="arrow"></b><ul class="submenu"><li class="active"><a href="/docs/index?id=48"><span>word.eval/word.eval.pro(单词)</span></a><b class="arrow"></b></li><li class=""><a href="/docs/index?id=117"><span>sent.eval/sent.eval.pro(句子)</span></a><b class="arrow"></b></li><li class=""><a href="/docs/index?id=118"><span>para.eval(段落)</span></a><b class="arrow"></b></li></ul></li></ul></li><li class=""><a href="#" class="dropdown-toggle"><span>错误码</span><b class="arrow fa fa-angle-down"></b></a><b class="arrow"></b><ul class="submenu"><li class=""><a href="/docs/index?id=26"><span>通用SDK错误码</span></a><b class="arrow"></b></li><li class=""><a href="/docs/index?id=25"><span>服务端错误码</span></a><b class="arrow"></b></li></ul></li></ul></li><li class=""><a href="#" class="dropdown-toggle"><span class="menu-text">下载中心</span><b class="arrow fa fa-angle-down"></b></a><b class="arrow"></b><ul class="submenu"><li class=""><a href="#" class="dropdown-toggle"><span>SDK下载</span><b class="arrow fa fa-angle-down"></b></a><b class="arrow"></b><ul class="submenu"><li class=""><a href="/sdks/index?id=89"><span>通用SDK(3.0)</span></a><b class="arrow"></b></li></ul></li><li class=""><a href="#" class="dropdown-toggle"><span>demo下载</span><b class="arrow fa fa-angle-down"></b></a><b class="arrow"></b><ul class="submenu"><li class=""><a href="/sdks/index?id=103"><span>flutter_demo</span></a><b class="arrow"></b></li></ul></li></ul></li></ul>
				</ul><!-- /.nav-list -->

				<!-- /section:basics/sidebar.layout.minimize -->
			</div>
			<div class="ia-splitter-handle" title="双击还原间距"><div class="ia-splitter-handle-highlight splitter-icon-grab-handle"></div></div>
            

<div class="main-content">
    <div class="main-content-inner">
        <!-- #section:basics/content.breadcrumbs -->
        <div class="breadcrumbs ace-save-state adj" id="breadcrumbs">
            <ul class="breadcrumb">
                <li>
                    <i class="ace-icon fa fa-home home-icon"></i>
                    <a href="/">首页</a>
                </li>
                <li>开发文档</li>
                <li>评测参数及结果说明</li><li>英文</li>
                <li class="active">word.eval&#47;word.eval.pro(单词)</li>
            </ul><!-- /.breadcrumb -->
            <div class="nav-search">

                <button class="btn btn-link" data-action='favourite' data-id='48'>关注</button>






            </div>
        </div>

        <!-- /section:basics/content.breadcrumbs -->
        <div class="page-content" style="background-color: #FFF !important">
            <!-- <div class="page-header">
                <div class="row">
                    <div class="col-md-12">
                        
                    </div>
                </div>
            </div> --><!-- /.page-header -->
            <div>
                <div class="row" style="margin-left:0px;margin-right:0px;">
                    <div class="col-md-10 content ck-content" id='content' style="padding-left:40px;padding-top: 24px;">

                            <h2><span style="color:rgb(0,0,0);"><strong>request参数</strong></span></h2><figure class="table"><table><tbody><tr><td style="text-align:center;"><span style="color:rgb(0,0,0);">字段</span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);">类型</span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);">是否必须</span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);">默认值</span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);">说明</span></td></tr><tr><td><span style="color:rgb(0,0,0);">coreType</span></td><td><span style="color:rgb(0,0,0);">字符串</span></td><td><span style="color:rgb(0,0,0);">是</span></td><td>&nbsp;</td><td><p><span style="color:rgb(0,0,0);">word.eval：英文单词、音标评测，word.eval.pro： 英文音标、单词自适应年龄段评测</span></p><p><span style="color:hsl(0,75%,60%);"><strong>注意：允许最大录音时长 20s</strong></span></p></td></tr><tr><td><span style="color:rgb(0,0,0);">getParam</span></td><td>整型</td><td><span style="color:rgb(0,0,0);">否</span></td><td>1</td><td><span style="color:rgb(0,0,0);">返回结果是否含请求参数，可选值：1、0，1为开启，0为关闭</span></td></tr><tr><td><span style="color:rgb(0,0,0);">refText</span></td><td><span style="color:rgb(0,0,0);">字符串</span></td><td><span style="color:rgb(0,0,0);">是</span></td><td>&nbsp;</td><td><p><span style="color:rgb(0,0,0);">评测文本，</span><span style="color:hsl(0,75%,60%);"><strong>1个单词或1个音标</strong></span><strong>，</strong><span style="color:rgb(0,0,0);">音标格式：/</span><span style="color:rgb(57,57,57);">æ</span><span style="color:rgb(0,0,0);">/</span></p><p><span style="color:rgb(0,0,0);">支持任意音标评测，音标需与dict_type设置的类型一致（若dict_type设置为IPA88，refText需要传IPA88音标评测）</span></p><p><span style="color:rgb(0,0,0);">1. 48个元辅音 ，如： /</span><span style="color:rgb(57,57,57);">æ/、/p/</span></p><p><span style="color:rgb(57,57,57);">2. 48个元辅音的任意组合，如：/kl/、/suː/</span></p><p><span style="color:rgb(57,57,57);">3. 单词音标评测，支持多种书写方式 ，如：/ˈwɔːtə(r)/、/ʰwaɪ/、/bɜːʴθdeɪ/</span></p></td></tr><tr><td><span style="color:rgb(0,0,0);">attachAudioUrl</span></td><td><span style="color:rgb(0,0,0);">整型</span></td><td><span style="color:rgb(0,0,0);">否</span></td><td><span style="color:rgb(0,0,0);">0</span></td><td><span style="color:rgb(0,0,0);">返回结果是否含音频下载地址，可选值：1、0，</span><span style="color:hsl(0,75%,60%);">提示：音频保留7天，如需长期保存，建议下载到自己服务器。</span></td></tr><tr><td><span style="color:rgb(0,0,0);">dict_type</span></td><td><span style="color:rgb(0,0,0);">字符串</span></td><td><span style="color:rgb(0,0,0);">否</span></td><td><span style="color:rgb(0,0,0);">KK</span></td><td><span style="color:rgb(0,0,0);">返回音素类型，可选值：CMU/KK/IPA88</span></td></tr><tr><td><span style="color:rgb(0,0,0);">phoneme_output</span></td><td><span style="color:rgb(0,0,0);">整型</span></td><td><span style="color:rgb(0,0,0);">否</span></td><td><span style="color:rgb(0,0,0);">1</span></td><td><span style="color:rgb(0,0,0);">返回结果是否含音素维度，可选值：0、1</span></td></tr><tr><td><span style="color:rgb(0,0,0);">agegroup</span></td><td><span style="color:rgb(0,0,0);">整型</span></td><td><span style="color:rgb(0,0,0);">否</span></td><td><span style="color:rgb(0,0,0);">3</span></td><td><p><span style="color:rgb(0,0,0);">年龄段支持可选值：1、2、3</span></p><p><span style="color:rgb(0,0,0);">1：3-6years old</span></p><p><span style="color:rgb(0,0,0);">2：6-12years old</span></p><p><span style="color:rgb(0,0,0);">3：&gt;12years old</span></p><p><span style="color:hsl(0,75%,60%);">注意：当coreType为word.eval.pro /sent.eval.pro时，该参数无效</span></p></td></tr><tr><td><span style="color:rgb(0,0,0);">slack</span></td><td><span style="color:rgb(0,0,0);">整型或浮点型</span></td><td><span style="color:rgb(0,0,0);">否</span></td><td><span style="color:rgb(0,0,0);">0</span></td><td><span style="color:rgb(0,0,0);">打分松紧度，取值范围[-1,1]，正数加分负数减分，0.1加分幅度较小，1加分幅度较大，干预后影响各维度得分</span></td></tr><tr><td><span style="color:rgb(0,0,0);">scale</span></td><td><span style="color:rgb(0,0,0);">整型或浮点型</span></td><td><span style="color:rgb(0,0,0);">否</span></td><td>100</td><td><span style="color:rgb(0,0,0);">分制，取值范围(0,100]，干预后影响各维度得分</span></td></tr><tr><td><span style="color:rgb(0,0,0);">precision</span></td><td><span style="color:rgb(0,0,0);">整型或浮点型</span></td><td><span style="color:rgb(0,0,0);">否</span></td><td><span style="color:rgb(0,0,0);">1</span></td><td><span style="color:rgb(0,0,0);">得分精度，取值范围(0,1]，干预后影响各维度得分</span></td></tr><tr><td><span style="color:rgb(0,0,0);">customized_lexicon</span></td><td><span style="color:rgb(0,0,0);">对象</span></td><td><span style="color:rgb(0,0,0);">否</span></td><td>&nbsp;</td><td><p><span style="color:rgb(0,0,0);">指定特定单词期望发音，使用 CMU音素符号，单词、CMU大小写不敏感。支持多单词、多音标</span></p><p><span style="color:rgb(0,0,0);">如:</span></p><p><span style="color:rgb(0,0,0);">"customized_lexicon":</span></p><p><span style="color:rgb(0,0,0);">{"but": [</span></p><p><span style="color:rgb(0,0,0);">&nbsp; &nbsp; ["B", "IY","Y","UW","T","IY"],</span></p><p><span style="color:rgb(0,0,0);">&nbsp; &nbsp; ["B","UH","T"]</span></p><p><span style="color:rgb(0,0,0);">]}</span></p></td></tr><tr><td><span style="color:rgb(0,0,0);">customized_pron</span></td><td><span style="color:rgb(0,0,0);">对象</span></td><td><span style="color:rgb(0,0,0);">否</span></td><td>&nbsp;</td><td><p><span style="color:rgb(0,0,0);">指定单词期望发音，可选 KK/IPA88，单词大小写不敏感。支持多单词、多音标</span>，音标不需要//括起来, <span style="color:rgb(0,0,0);">支持多种音标书写方式， 如：wɔːtə(r)、bɜːʳθde</span></p><p><span style="color:rgb(0,0,0);">示例：</span></p><p><span style="color:rgb(0,0,0);">"customized_pron":{</span></p><p><span style="color:rgb(0,0,0);">&nbsp; &nbsp; "type": "KK",</span></p><p><span style="color:rgb(0,0,0);">&nbsp; &nbsp; &nbsp; "pron":{"but":[</span></p><p><span style="color:rgb(0,0,0);">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;"bət","bʌt"</span></p><p><span style="color:rgb(0,0,0);">&nbsp; &nbsp; &nbsp; &nbsp; ]}</span></p><p><span style="color:rgb(0,0,0);">}</span></p></td></tr><tr><td><span style="color:rgb(0,0,0);">dict_dialect</span></td><td><span style="color:rgb(0,0,0);">字符串</span></td><td><span style="color:rgb(0,0,0);">否</span></td><td>&nbsp;</td><td><p><span style="color:rgb(0,0,0);">可选值：en_br、en_us。en_br表示限定英式发音评测；en_us表示限定美式发音评测；如果不设置此参数，表示不限定英式、美式发音。</span></p><p><span style="color:rgb(0,0,0);">注：发音/o/不同dict_type、dict_dialect值时，返回的样例：</span></p><p>&nbsp; &nbsp; &nbsp; &nbsp; dict_type &nbsp; &nbsp; | &nbsp; &nbsp; &nbsp;dict_dialect &nbsp; &nbsp; &nbsp;| &nbsp; &nbsp; &nbsp;发音/o/返回示例<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; KK &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;| &nbsp; &nbsp; &nbsp; &nbsp; 不设置 &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;| &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; o<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; IPA88 &nbsp; &nbsp; | &nbsp; &nbsp; &nbsp; &nbsp; 不设置 &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;| &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;əʊ<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; KK &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;| &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;en_us &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;| &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; o<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; KK &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;| &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;en_br &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;| &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; o<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; IPA88 &nbsp; &nbsp; | &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;en_us &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;| &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;oʊ<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; IPA88 &nbsp; &nbsp; | &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;en_br &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;| &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; əʊ<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;不设置 &nbsp; &nbsp;| &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;en_us &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;| &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;o<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;不设置 &nbsp; &nbsp;| &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;en_br &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;| &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;o</p></td></tr><tr><td>blend_phoneme</td><td><span style="color:rgb(0,0,0);">整型</span></td><td>否</td><td>0</td><td><p>默认为0，可选值：0、1，0表示关闭，1表示开启</p><p>双元音：/ɪə/、/eə/、/ʊə/<br>其他辅音：/tr/、/ts/、/dr/、/dz/<br>以上音素，设置为1后，会合在一起返回</p><p>示例：</p><p>单词track，音标/træk/，result.words[].phonemes字段：<br>开启前返回的音素划分是：/t/ /r/ /æ/ /k/<br>开启后返回的音素划分是：/tr/ /æ/ /k/</p></td></tr><tr><td>blend_phonics</td><td><span style="color:rgb(0,0,0);">整型</span></td><td>否</td><td>0</td><td><p>默认为0，可选值：0、1，0表示关闭，1表示开启</p><p>双元音：/ɪə/、/eə/、/ʊə/<br>其他辅音：/tr/、/ts/、/dr/、/dz/<br>以上音素，设置为1后，会合在一起返回</p><p>示例：</p><p>单词track，音标/træk/，result.words[].phonics字段：<br>开启前返回的phonics划分是：t→/t/、r→/r/、a→/æ/、ck→/k/<br>开启后返回的phonics划分是：tr→/t/ /r/、a→/æ/、ck→/k/</p></td></tr><tr><td>enable_digit_by_digit_read</td><td><span style="color:rgb(0,0,0);">整型</span></td><td>否</td><td>0</td><td><p>允许数字拆分成单个数字读，默认为0，可选值：0、1，0表示关闭，1表示开启</p><p>示例：1234 5678 001 &nbsp;可以按照1 2 3 4 5 6 7 8 0 0 1去读</p></td></tr></tbody></table></figure><h2><strong>结果示例（有errId返回结果）</strong></h2><pre><code class="language-plaintext">{
    "applicationId": "xxx",
    "dtLastResponse": "2025-06-09 14:48:20:354",
    "eof": 1,
    "errId": xxx,
    "error": "xx",
    "params": {
        "app": {
            "applicationId": "xxx",
            "sig": "xxx",
            "timestamp": "1749451700",
            "userId": "xxx"
        },
        "audio": {
            "audioType": "opus",
            "channel": 1,
            "sampleBytes": 2,
            "sampleRate": 16000
        },
        "coreProvideType": "cloud",
        "request": {
            "coreType": "word.eval",
            "phoneme_output": 1,
            "refText": "nice to meet you",
            "tokenId": "684683b47872855f6c000001"
        }
    },
    "recordId": "xxx",                       //注：建议业务层保存，方便排查错误
    "refText": "nice to meet you",
    "tokenId": "xxx"
}</code></pre><h2>&nbsp;</h2><h2><strong>结果示例</strong></h2><pre><code class="language-plaintext">{
    "recordId": "xxx",                            //该次评测唯一标识  注：建议业务层保存，方便排查错误
    "eof": 1,                                     //最终结果标识                           
    "tokenId": "xxx",                             //终端用户请求id
    "audioUrl": "xxx",                            //音频地址
    "refText": "new",                             //评测文本
    "dtLastResponse": "2020-12-11 10:31:06:706",  //结果返回时间
    "result": {                                   //评测结果集
        "resource_version": "2.8.7",              //资源版本
        "stress": 100,                            //重音节发音是否正确100表示重读音节正确0表示重读音节错误
        "pause_count": 0,
        "warning": [{                             
                "message": "Audio volume too low!",
                "code": 1002
            }
        ],
        "words": [{                               //单词得分详情
                "charType": 0,                    //0:非标点文本，1:标点符号                
                "pause":{						  //停顿检测
                    "type":0,
                    "duration":0
                },
                "readType":4,
                "repetition":[                   //实际发音时重复朗读，则显示该字段，否则不显示
                    {
                        "start":40,
                        "overall":92,
                        "end":158
                    },
                    {
                        "start":201,
                        "overall":65,
                        "end":308
                    },
                    {
                        "start":348,
                        "overall":70,
                        "end":434
                    }
                ],
                "phonemes": [{                    //音素信息, 需要开启 phoneme_output
                        "pronunciation": 100,     //音素的发音得分
                        "span": {                 //音素在音轨上的时间信息
                            "end": 89,            //音素音素在音轨上的结束时间
                            "start": 75           //音素音素在音轨上的开始时间
                        },
                        "phoneme": "n",            //音素的发音
                        "stress_mark": 0           //该音素前的重音符号信息。0表示该音素前无重音符号、1表示该音素前有重音符号（ˈ）、2表示该音素前有次重音符号（ˌ）
                    }, {
                        "pronunciation": 100,     
                        "span": {
                            "end": 100,
                            "start": 89
                        },
                        "phoneme": "j",
                        "stress_mark": 0
                    }, {
                        "pronunciation": 93,
                        "span": {
                            "end": 120,
                            "start": 100
                        },
                        "phoneme": "uː",
                        "stress_mark": 0
                    }
                ],
                "span": {                         //单词在音轨上时间信息集
                    "end": 120,
                    "start": 75
                },
                "phonics": [{                     //已划分字母、音标对应关系时，phonics字段返回示例
                        "overall": 100,           //字母得分
                        "spell": "n",             //字母拼写
                        "phoneme": [              //字母音素信息集
                            "n"
                        ]
                    }, {
                        "overall": 100,
                        "spell": "e",
                        "phoneme": [
                            "j"
                        ]
                    }, {
                        "overall": 93,
                        "spell": "w",
                        "phoneme": [
                            "uː"
                        ]
                    }
                ],
                "phonics": [{                     //未划分字母、音标对应关系时，phonics字段返回示例
                        "overall": 100,           //字母得分
                        "spell": "new",           //字母拼写
                        "phoneme": [              //字母音素信息集
                            "n",
                            "j",
                            "uː"
                        ]
                    }
                ],
                "scores": {                      //单词得分详情
                    "overall": 99,               //单词总分   
                    "stress": [{                 //重读音节信息集，注：数组为1时不建议显示重音节符号
                            "ref_stress": 1,     //本音节的参考重音(1 表示重读音节 2 表示次重音节 0 表示非重读)
                            "stress": 0,         //本音节的实际重音(1 表示重读音节 2 表示次重音节 0 表示非重读)
                            "overall": 98,       //单词总分
                            "spell": "new",      //本音节对应的拼写
                            "phoneme_offset": 0, //音节在整个音标的起始位置
                            "phonetic": "njuː"   //本音节对应音标
                        }
                    ],
                    "pronunciation": 99          //发音得分
                },
                "word_parts": [{                 //单词类型信息集
                        "charType": 0,           //0:英文或中文或法语，1:标点符号
                        "part": "new",            //文本
                        "beginIndex":0,
                        "endIndex":3
                    }
                ],
                "word": "new"                    //评测文本
            }
        ],
        "duration": "1.556",                     //音频时长
        "numeric_duration":	1.556,
        "overall": 99,                           //总分
        "kernel_version": "4.9.3",               //内核版本
        "pronunciation": 99                      //发音得分
    },
    "params": {                                  //参数信息集，详情请参考评测参数-英文
        "audio": {
            "audioType": "opus",
            "sampleBytes": 2,
            "sampleRate": 16000,
            "channel": 1
        },
        "app": {
            "userId": "xxx",
            "timestamp": "1607653865",
            "applicationId": "xxx",
            "sig": "3a817a3ffb78b11a86a98f7df811230e8d3493d1"
        },
        "request": {
            "attachAudioUrl": 1,
            "dict_type": "IPA88",
            "coreType": "word.eval.pro",
            "tokenId": "xxx",
            "refText": "new"
        }
    },
    "applicationId": "xxx"
}
</code></pre><p>&nbsp;</p><h2><span style="color:rgb(0,0,0);"><strong>结果字段说明</strong></span></h2><figure class="table"><table><tbody><tr><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>属性</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>类型</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>含义</strong></span></td></tr><tr><td><span style="color:rgb(0,0,0);">tokenId</span></td><td><span style="color:rgb(0,0,0);">字符串</span></td><td><span style="color:rgb(0,0,0);">终端用户请求id</span></td></tr><tr><td><span style="color:rgb(0,0,0);">refText</span></td><td><span style="color:rgb(0,0,0);">字符串</span></td><td>评测文本</td></tr><tr><td><span style="color:rgb(0,0,0);">audioUrl</span></td><td><span style="color:rgb(0,0,0);">字符串</span></td><td><span style="color:rgb(0,0,0);">在线音频地址，</span><span style="color:hsl(0,75%,60%);">需设置attachAudioUrl</span></td></tr><tr><td><span style="color:rgb(0,0,0);">dtLastResponse</span></td><td><span style="color:rgb(0,0,0);">字符串</span></td><td><span style="color:rgb(0,0,0);">评分结果返回时间</span></td></tr><tr><td><span style="color:rgb(0,0,0);">result</span></td><td><span style="color:rgb(0,0,0);">对象</span></td><td><span style="color:rgb(0,0,0);">评分结果</span></td></tr><tr><td><span style="color:rgb(0,0,0);">applicationId</span></td><td><span style="color:rgb(0,0,0);">字符串</span></td><td><span style="color:rgb(0,0,0);">appKey</span></td></tr><tr><td><span style="color:rgb(0,0,0);">recordId</span></td><td><span style="color:rgb(0,0,0);">字符串</span></td><td><span style="color:rgb(0,0,0);">评分唯一id &nbsp;&nbsp;</span><span style="color:hsl(0, 75%, 60%);"> 注：建议业务层保存，方便排查错误</span></td></tr><tr><td><span style="color:rgb(0,0,0);">params</span></td><td><span style="color:rgb(0,0,0);">对象</span></td><td><span style="color:rgb(0,0,0);">请求参数，</span><span style="color:hsl(0,75%,60%);">需设置getParam</span></td></tr><tr><td><span style="color:rgb(0,0,0);">eof</span></td><td><span style="color:rgb(0,0,0);">整型</span></td><td><span style="color:rgb(0,0,0);">0：中间结果；1：最终结果</span></td></tr><tr><td>errId</td><td><span style="color:rgb(0,0,0);">整型</span></td><td>错误码 &nbsp; &nbsp;<span style="color:hsl(0,75%,60%);">注：当出现该字段时，返回结果无result字段</span></td></tr><tr><td>error</td><td><span style="color:rgb(0,0,0);">字符串</span></td><td>错误码信息 &nbsp;&nbsp;<span style="color:hsl(0,75%,60%);"> 注：当出现该字段时，返回结果无result字段</span></td></tr></tbody></table></figure><h4>&nbsp;</h4><h4><span style="color:rgb(0,0,0);"><strong>result&nbsp;</strong></span></h4><p>&nbsp;<span style="color:hsl(0,75%,60%);">注：当返回结果出现errId字段时，无该字段</span></p><figure class="table"><table><tbody><tr><td style="text-align:center;"><strong>属性</strong></td><td style="text-align:center;"><strong>类型</strong></td><td style="text-align:center;"><strong>含义</strong></td></tr><tr><td><span style="color:hsl(0,0%,0%);">words</span></td><td><span style="color:hsl(0,0%,0%);">对象数组</span></td><td><span style="color:hsl(0,0%,0%);">单词</span></td></tr><tr><td><span style="color:hsl(0,0%,0%);">duration</span></td><td><span style="color:hsl(0,0%,0%);">字符串</span></td><td><span style="color:hsl(0,0%,0%);">音频时长。单位：秒</span></td></tr><tr><td><span style="color:hsl(0,0%,0%);">stress&nbsp;</span></td><td><span style="color:hsl(0,0%,0%);">整型</span></td><td><span style="color:hsl(0,0%,0%);">重</span><span style="color:rgb(0,0,0);">(zhòng)</span><span style="color:hsl(0,0%,0%);">音节发音是否正确。100表示重读音节正确、0表示重读音节错误</span></td></tr><tr><td><span style="color:hsl(0,0%,0%);">kernel_version</span></td><td><span style="color:hsl(0,0%,0%);">字符串</span></td><td><span style="color:hsl(0,0%,0%);">内核版本</span></td></tr><tr><td><span style="color:hsl(0,0%,0%);">resource_version</span></td><td><span style="color:hsl(0,0%,0%);">字符串</span></td><td><span style="color:hsl(0,0%,0%);">资源版本</span></td></tr><tr><td><span style="color:hsl(0,0%,0%);">overall</span></td><td><span style="color:hsl(0,0%,0%);">整型或浮点型</span></td><td><span style="color:hsl(0,0%,0%);">总分。</span><span style="color:rgb(230,76,76);">若 precision（精度）设置为浮点型时，该值显示为浮点型（若该值小数 点后为 0，则显示成整型），若 scale（分 制）设置为（0,1），该值显示为浮点型， 否则为整型。</span></td></tr><tr><td><span style="color:hsl(0,0%,0%);">pronunciation</span></td><td><span style="color:hsl(0,0%,0%);">整型或浮点型</span></td><td><span style="color:hsl(0,0%,0%);">发音得分。</span><span style="color:rgb(230,76,76);">若 precision（精度）设置为浮点型时，该值显示为浮点型（若该值小数 点后为 0，则显示成整型），若 scale（分 制）设置为（0,1），该值显示为浮点型， 否则为整型。</span></td></tr><tr><td><span style="color:hsl(0,0%,0%);">warning</span></td><td><span style="color:hsl(0,0%,0%);">对象数组</span></td><td><span style="color:hsl(0,0%,0%);">音频检测提示。 </span><span style="color:hsl(0,75%,60%);">注：不是必现字段，业务层使用前需先判断该字段是否存在，</span><span style="color:rgb(230,76,76);">详情请参考：</span><a href="/docs/index?id=25" target="_blank"><span style="color:rgb(230,76,76);">服务端错误码</span></a></td></tr><tr><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;">pause_count</td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;">整型</td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;">停顿次数</td></tr><tr><td>numeric_duration</td><td><span style="color:hsl(0,0%,0%);">整型或浮点型</span></td><td>音频时长<span style="color:hsl(0,0%,0%);">。单位：秒</span></td></tr></tbody></table></figure><p>&nbsp;</p><p><span style="color:rgb(0,0,0);"><strong>result-&gt;warning</strong></span></p><figure class="table"><table><tbody><tr><td><strong>属性</strong></td><td><strong>类型</strong></td><td><strong>含义</strong></td></tr><tr><td>code</td><td>整型</td><td>错误码</td></tr><tr><td>message</td><td>字符串</td><td>音频检测的提示信息，请参考<a href="/docs/index?id=25" target="_blank">服务端错误码</a></td></tr></tbody></table></figure><p>&nbsp;</p><p><span style="color:rgb(0,0,0);"><strong>result-&gt;words</strong></span></p><figure class="table"><table><tbody><tr><td style="text-align:center;"><strong>属性</strong></td><td style="text-align:center;"><strong>类型</strong></td><td style="text-align:center;"><strong>含义</strong></td></tr><tr><td><span style="color:hsl(0,0%,0%);">scores</span></td><td><span style="color:hsl(0,0%,0%);">对象</span></td><td><span style="color:hsl(0,0%,0%);">单词拆分成音标得分详情</span></td></tr><tr><td><span style="color:hsl(0,0%,0%);">charType</span></td><td>整型</td><td><span style="color:hsl(0,0%,0%);">0：非标点符号，1：标点符号</span></td></tr><tr><td><span style="color:hsl(0,0%,0%);">word</span></td><td><span style="color:hsl(0,0%,0%);">字符串</span></td><td>单词</td></tr><tr><td><span style="color:hsl(0,0%,0%);">span</span></td><td><span style="color:hsl(0,0%,0%);">对象</span></td><td><span style="color:hsl(0,0%,0%);">单词在音轨上的时间范围</span></td></tr><tr><td><span style="color:hsl(0,0%,0%);">phonemes</span></td><td><span style="color:hsl(0,0%,0%);">对象数组</span></td><td><span style="color:hsl(0,0%,0%);">音素信息，</span><span style="color:hsl(0,75%,60%);">需要开启phoneme_output</span></td></tr><tr><td><span style="color:hsl(0,0%,0%);">word_parts&nbsp;</span></td><td><span style="color:hsl(0,0%,0%);">对象数组</span></td><td><span style="color:hsl(0,0%,0%);">单词文本信息</span></td></tr><tr><td>pause</td><td>对象</td><td>停顿检测</td></tr><tr><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;"><span style="color:rgb(0,0,0);">readType</span></td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;"><span style="color:rgb(0,0,0);">整型</span></td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;"><span style="color:rgb(0,0,0);">值为0表示正常读，值为1表示该音素前有多读或错读的音素，值为2表示该音素后有多读或错读的音素，值为3表示漏读, &nbsp;值为4表示重复读</span></td></tr><tr><td>repetition</td><td><span style="color:hsl(0,0%,0%);">对象数组</span></td><td>重复读单词打分。<span style="color:hsl(0,75%,60%);">注：实际重复读则显示该结果，否则不显示</span></td></tr><tr><td>phonics</td><td><span style="color:hsl(0,0%,0%);">对象数组</span></td><td>发音字母组合</td></tr></tbody></table></figure><p>&nbsp;</p><p><span style="color:rgb(0,0,0);"><strong>result-&gt;words- -&gt; word_parts</strong></span></p><figure class="table"><table><tbody><tr><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>属性</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>类型</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>含义</strong></span></td></tr><tr><td><span style="color:rgb(0,0,0);">part&nbsp;</span></td><td><span style="color:rgb(0,0,0);">字符串</span></td><td><span style="color:rgb(0,0,0);">文本</span></td></tr><tr><td><span style="color:rgb(0,0,0);">charType</span></td><td><span style="color:rgb(0,0,0);">整型</span></td><td><span style="color:hsl(0,0%,0%);">0：非标点符号，1：标点符号</span></td></tr><tr><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;">beginIndex</td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;">整型</td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;">单词在文本中开始的位置</td></tr><tr><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;">endIndex</td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;">整型</td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;">单词在文本中结束的位置</td></tr></tbody></table></figure><p><strong>result-&gt;words-&gt;pause</strong></p><figure class="table"><table><tbody><tr><td style="text-align:center;"><strong>属性</strong></td><td style="text-align:center;"><strong>类型</strong></td><td style="text-align:center;"><strong>含义</strong></td></tr><tr><td>type</td><td>整型</td><td>duration&gt;10该值为1，否则值为0&nbsp;</td></tr><tr><td>duration</td><td>整型</td><td>停顿时间，单位：10毫秒</td></tr></tbody></table></figure><p><span style="color:rgb(0,0,0);"><strong>result-&gt;words- -&gt;repetition</strong></span><span style="color:hsl(0,75%,60%);"><strong>（</strong>实际重复读则显示该结果，否则不显示<strong>）</strong></span></p><figure class="table"><table><tbody><tr><td style="text-align:center;"><strong>属性</strong></td><td style="text-align:center;"><strong>类型</strong></td><td style="text-align:center;"><strong>含义</strong></td></tr><tr><td>start</td><td>整型</td><td><span style="color:hsl(0,0%,0%);">单词在音轨上的开始时间，单位：10毫秒</span></td></tr><tr><td>end</td><td>整型</td><td><span style="color:hsl(0,0%,0%);">单词在音轨上的结束时间，单位：10毫秒</span></td></tr><tr><td>overall</td><td>整型</td><td>单词的分数</td></tr></tbody></table></figure><p><span style="color:rgb(0,0,0);"><strong>result-&gt;words-&gt;scores</strong></span></p><figure class="table"><table><tbody><tr><td style="text-align:center;"><strong>属性</strong></td><td style="text-align:center;"><strong>类型</strong></td><td style="text-align:center;"><strong>含义</strong></td></tr><tr><td><span style="color:hsl(0,0%,0%);">overall</span></td><td><span style="color:hsl(0,0%,0%);">整型或浮点型</span></td><td><span style="color:hsl(0,0%,0%);">总分。</span><span style="color:rgb(230,76,76);">若 precision（精度）设置为浮点型时，该值显示为浮点型（若该值小数 点后为 0，则显示成整型），若 scale（分 制）设置为（0,1），该值显示为浮点型， 否则为整型。</span></td></tr><tr><td><span style="color:hsl(0,0%,0%);">pronunciation</span></td><td><span style="color:hsl(0,0%,0%);">整型或浮点型</span></td><td><span style="color:hsl(0,0%,0%);">发音得分。</span><span style="color:rgb(230,76,76);">若 precision（精度）设置为浮点型时，该值显示为浮点型（若该值小数 点后为 0，则显示成整型），若 scale（分 制）设置为（0,1），该值显示为浮点型， 否则为整型。</span></td></tr><tr><td><span style="color:hsl(0,0%,0%);">stress&nbsp;</span></td><td><span style="color:hsl(0,0%,0%);">对象数组</span></td><td><span style="color:hsl(0,0%,0%);">重</span><span style="color:rgb(0,0,0);">(zhòng)</span><span style="color:hsl(0,0%,0%);">读音节信息</span></td></tr></tbody></table></figure><p>&nbsp;</p><p><span style="color:rgb(0,0,0);"><strong>result- - &gt;words- - &gt;scores- -&gt; stress</strong></span></p><p><span style="color:hsl(0,75%,60%);"><strong>注：数组为1时表示单音节音标，不建议显示重音符号</strong></span></p><figure class="table"><table><tbody><tr><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>属性</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>类型</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>含义</strong></span></td></tr><tr><td><span style="color:hsl(0,0%,0%);">phonetic</span></td><td><span style="color:hsl(0,0%,0%);">字符串</span></td><td><span style="color:hsl(0,0%,0%);">本音节对应音标</span></td></tr><tr><td><span style="color:hsl(0,0%,0%);">spell&nbsp;</span></td><td><span style="color:hsl(0,0%,0%);">字符串</span></td><td><span style="color:hsl(0,0%,0%);">本音节对应的拼写</span></td></tr><tr><td><span style="color:hsl(0,0%,0%);">stress</span></td><td><span style="color:hsl(0,0%,0%);">整型</span></td><td><span style="color:hsl(0,0%,0%);">本音节的实际重</span><span style="color:rgb(0,0,0);">(zhòng)</span><span style="color:hsl(0,0%,0%);">音。1表示重</span><span style="color:rgb(0,0,0);">(zhòng)</span><span style="color:hsl(0,0%,0%);">读音节、2表示次重</span><span style="color:rgb(0,0,0);">(zhòng)</span><span style="color:hsl(0,0%,0%);">音节、0表示非重</span><span style="color:rgb(0,0,0);">(zhòng)</span><span style="color:hsl(0,0%,0%);">读</span></td></tr><tr><td><span style="color:hsl(0,0%,0%);">ref_stress</span></td><td><span style="color:rgb(0,0,0);">整型</span></td><td><span style="color:hsl(0,0%,0%);">本音节的参考重</span><span style="color:rgb(0,0,0);">(zhòng)</span><span style="color:hsl(0,0%,0%);">音。1表示重</span><span style="color:rgb(0,0,0);">(zhòng)</span><span style="color:hsl(0,0%,0%);">读音节、2表示次重</span><span style="color:rgb(0,0,0);">(zhòng)</span><span style="color:hsl(0,0%,0%);">音节、0表示非重</span><span style="color:rgb(0,0,0);">(zhòng)</span><span style="color:hsl(0,0%,0%);">读</span></td></tr><tr><td><span style="color:hsl(0,0%,0%);">phoneme_offset</span></td><td><span style="color:hsl(0,0%,0%);">整型</span></td><td><span style="color:hsl(0,0%,0%);">音节在整个音标的起始位置</span></td></tr><tr><td><span style="color:hsl(0,0%,0%);">overall</span></td><td><span style="color:hsl(0,0%,0%);">整型或浮点型</span></td><td><span style="color:hsl(0,0%,0%);">音节总分。</span><span style="color:rgb(230,76,76);">若 precision（精度）设置为浮点型时，该值显示为浮点型（若该值小数 点后为 0,则显示成整型），若 scale（分 制）设置为（0,1）, 该值显示为浮点型， 否则为整型。</span></td></tr></tbody></table></figure><p>&nbsp;</p><p><span style="color:rgb(0,0,0);"><strong>result-&gt;words-&gt;phonics</strong></span></p><figure class="table"><table><tbody><tr><td style="text-align:center;"><strong>属性</strong></td><td style="text-align:center;"><strong>类型</strong></td><td style="text-align:center;"><strong>含义</strong></td></tr><tr><td><span style="color:hsl(0,0%,0%);">overall</span></td><td><span style="color:hsl(0,0%,0%);">整型或浮点型</span></td><td><span style="color:hsl(0,0%,0%);">每个发音字母总分。</span><span style="color:rgb(230,76,76);">若 precision（精度）设置为浮点型时，该值显示为浮点型（若该值小数 点后为0，则显示成整型），若 scale（分 制）设置为（0,1），该值显示为浮点型， 否则为整型。</span></td></tr><tr><td><span style="color:hsl(0,0%,0%);">phoneme</span></td><td><span style="color:hsl(0,0%,0%);">字符串数组</span></td><td><span style="color:hsl(0,0%,0%);">字母音素</span></td></tr><tr><td><span style="color:hsl(0,0%,0%);">spell</span></td><td><span style="color:hsl(0,0%,0%);">字符串</span></td><td><span style="color:hsl(0,0%,0%);">字母拼写</span></td></tr></tbody></table></figure><p>&nbsp;</p><p><span style="color:rgb(0,0,0);"><strong>result-&gt;words-&gt;span</strong></span></p><figure class="table"><table><tbody><tr><td style="text-align:center;"><strong>属性</strong></td><td style="text-align:center;"><strong>类型</strong></td><td style="text-align:center;"><strong>含义</strong></td></tr><tr><td><span style="color:hsl(0,0%,0%);">end</span></td><td><span style="color:hsl(0,0%,0%);">整型</span></td><td><span style="color:hsl(0,0%,0%);">单词在音轨上的结束时间，单位：10毫秒</span></td></tr><tr><td><span style="color:hsl(0,0%,0%);">start</span></td><td><span style="color:rgb(0,0,0);">整型</span></td><td><span style="color:hsl(0,0%,0%);">单词在音轨上的开始时间，单位：10毫秒</span></td></tr></tbody></table></figure><p>&nbsp;</p><p><span style="color:rgb(0,0,0);"><strong>result-&gt;words-&gt; phonemes</strong></span></p><figure class="table"><table><tbody><tr><td style="text-align:center;"><strong>属性</strong></td><td style="text-align:center;"><strong>类型</strong></td><td style="text-align:center;"><strong>含义</strong></td></tr><tr><td><span style="color:hsl(0,0%,0%);">span</span></td><td>对象</td><td><span style="color:hsl(0,0%,0%);">音素在音轨上的时间</span></td></tr><tr><td><span style="color:hsl(0,0%,0%);">phoneme</span></td><td><span style="color:hsl(0,0%,0%);">字符串</span></td><td><span style="color:hsl(0,0%,0%);">音素的发音</span></td></tr><tr><td><span style="color:hsl(0,0%,0%);">pronunciation</span></td><td><span style="color:rgb(0,0,0);">整型或浮点型</span></td><td><span style="color:hsl(0,0%,0%);">音素发音得分。</span><span style="color:rgb(230,76,76);">若 precision（精度）设置为浮点型时，该值显示为浮点型（若该值小数 点后为 0，则显示成整型），若 scale（分 制）设置为（0,1），该值显示为浮点型， 否则为整型。</span></td></tr><tr><td>stress_mark</td><td><span style="color:rgb(0,0,0);">整型</span></td><td>该音素前的重音符号信息。0表示该音素前无重音符号、1表示该音素前有重音符号（ˈ）、2表示该音素前有次重音符号（ˌ）</td></tr></tbody></table></figure><p>&nbsp;</p><p><span style="color:rgb(0,0,0);"><strong>result-&gt;words-&gt; phonemes-&gt;span</strong></span></p><figure class="table"><table><tbody><tr><td style="text-align:center;"><strong>属性</strong></td><td style="text-align:center;"><strong>类型</strong></td><td style="text-align:center;"><strong>含义</strong></td></tr><tr><td><span style="color:hsl(0,0%,0%);">end</span></td><td><span style="color:hsl(0,0%,0%);">整型</span></td><td><span style="color:hsl(0,0%,0%);">音素在音轨上的结束时间，单位：10毫秒</span></td></tr><tr><td><span style="color:hsl(0,0%,0%);">start</span></td><td><span style="color:hsl(0,0%,0%);">整型</span></td><td><span style="color:hsl(0,0%,0%);">音素在音轨上的开始时间，单位：10毫秒</span></td></tr></tbody></table></figure><p>&nbsp;</p>

                        <!-- <div id="editor" style="height:100%"></div> -->
                    </div>
                    <div id="catalog" class="col-md-2"></div>
                </div>
            </div>
        </div>
    </div>
</div>

                
			<div class="footer">
				<div class="footer-inner">
					<!-- #section:basics/footer -->
					<div class="footer-content">
						<span class="bigger-120">
							<span class="blue bolder">声通科技</span>
							&copy; 2025
						</span>
					</div>

					<!-- /section:basics/footer -->
				</div>
			</div>

			<a href="#" id="btn-scroll-up" class="btn-scroll-up btn btn-sm btn-inverse">
				<i class="ace-icon fa fa-angle-double-up icon-only bigger-110"></i>
			</a>
		</div><!-- /.main-container -->

		<!-- basic scripts -->

		<!--[if !IE]> -->
		<script src="/assets/js/jquery-2.2.4.min.js"></script>

		<!-- <![endif]-->

		<!--[if IE]>
        <script src="/components/jquery.1x/dist/jquery.js"></script>
        <![endif]-->
		<script type="text/javascript">
			if('ontouchstart' in document.documentElement) document.write("<script src='../components/_mod/jquery.mobile.custom/jquery.mobile.custom.js'>"+"<"+"/script>");
		</script>
		<script src="/components/bootstrap/dist/js/bootstrap.js"></script>

		<!-- page specific plugin scripts -->

		<!--[if lte IE 8]>
		  <script src="/components/ExplorerCanvas/excanvas.js"></script>
		<![endif]-->

        <!-- ace scripts -->
        
        <script src="/assets/js/ace-elements.min.js"></script>
		<script src="/assets/js/ace.min.js"></script>
		<script src="/components/jquery.gritter/js/jquery.gritter.min.js"></script>
		<script src="/components/bootbox/bootbox.all.min.js"></script>
		<script src="/assets/js/app.min.js"></script>
		<script src="/assets/js/search.min.js"></script>
		<!-- inline scripts related to this page -->
		<script type="text/JavaScript">
			jQuery(function($){
				//$(".nav-list .active").parent().parent().addClass("active open");

				$("a[data-action=edit-user]").on('click', function(){
					$this = $(this);
					var id = $this.data("id");
					var name = $this.data("name");
					var email = $this.data("email");
					dialog= bootbox.dialog({
						title: '编辑',
						buttons: {
							cancel: {
								label: "取消",
								className: 'btn-danger',
							},
							ok: {
								label: "确定",
								className: 'btn-info',
								callback: function(){
									$.ajax({
										type: "POST",
										url: "/users/edit_user",
										data: $("#user-form").serialize(),
										beforeSend: function() {
											var flag = true;
											$('#user-form input[required*=""]').each(function(){
												if($(this).val() == "") {
													$(this).focus();
													notice("","参数不能为空","warning");
													flag = false;
													return false;
												}
											});
											let password1 = $.trim($('input[name="password1"]').val());
											let password2 = $.trim($('input[name="password2"]').val());
											if ((password1 != '' || password2 != '') && password1!=password2) {
												notice("","两次输入密码不一致","warning");
												flag = false;
											}
											if(!flag) {
												return flag;
											}
											
										},
										success: function(data) {
											try {
												var obj = JSON.parse(data);
												if(obj.status == "ok") {
													$('.user-info span').text($('#user-form input[name=name]').val());
													$this.attr('data-name', $('#user-form input[name=name]').val());
													$this.attr('data-email', $('#user-form input[name=email]').val());
													notice("","保存成功","success");
													dialog.modal('hide');
												} else {
													notice("保存失败",obj.error,"error");
												}
											}catch(e) {
												notice("保存失败","","error");
											}
											
										},
										error: function(jqXHR, textStatus, errorThrown){
											console.log(jqXHR);
											console.log(textStatus);
											console.log(errorThrown);
											notice("保存失败",jqXHR.status + ':' + errorThrown,"error");
										},
										complete: function() {
											
										}
									})
									return false;
								}
							}
						},
						message: '<div class="row">'+
								'<div class="col-xs-12">'+
								'<form class="form-horizontal" id="user-form" role="form">' +
								'<div class="form-group">'+
								'<label class="col-sm-3 control-label no-padding-right required"> 昵称: </label>'+
								'<div class="col-sm-9">'+
								'<input type="text"  name="id" class="hidden" value="' + id + '"/>'+
								'<input type="text"  name="name" class="col-xs-10 col-sm-6" required value="' + name + '"/>'+
								'</div>'+
								'</div>'+
								'<div class="form-group">'+
								'<label class="col-sm-3 control-label no-padding-right required"> 邮箱: </label>'+
								'<div class="col-sm-9">'+
								'<input type="text"  name="email" class="col-xs-10 col-sm-6" required value="' + email + '"/>'+
								'</div>'+
								'</div>'+
								'<div class="form-group">'+
								'<label class="col-sm-3 control-label no-padding-right"> 密码: </label>'+
								'<div class="col-sm-9">'+
								'<input type="password" class="input-md" name="password1"/>&nbsp;'+
								'<input type="password" class="input-md" name="password2"/>'+
								'</div>'+
								'</div>'+
								'</form>'+
								'</div>'+
								'</div>',
					});
				});
			});
		</script>
		<script src="/assets/js/catalog.min.js"></script>
<script type="text/JavaScript">	
    jQuery(function($){
        doMenu($('#content'), $('#catalog'));
        $('table').addClass('table table-striped table-bordered table-hover');



            
        $('button[data-action="favourite"').on('click', function() {
            $this = $(this);
            let id = $(this).data('id');
            $.ajax({
                type: "POST",
                url: 'favourite',
                data: {id:id},
                beforeSend: function() {},
                success: function(data) {
                    try {
                        var obj = JSON.parse(data);
                        if(obj.status == "ok") {
                            let txt = $this.html();
                            if(txt=='已关注') {
                                $this.html('关注')
                                notice("","已取消关注","success");
                            } else {
                                $this.html('已关注');
                                if(obj.hasOwnProperty('error')) {
                                    notice("","已关注，" + obj.error,"warning");
                                }else{
                                    notice("","已关注","success");
                                }
                                
                            }
                        } else {
                            notice("操作失败",obj.error,"error");
                        }
                    }catch(e) {
                        console.log(e);
                        notice("操作失败","","error");
                    }
                    
                },
                error: function(jqXHR, textStatus, errorThrown){
                    console.log(jqXHR);
                    console.log(textStatus);
                    console.log(errorThrown);
                    notice("操作失败","","error");
                },
                complete: function() {
                }
            });
        })

    }); 
</script>

		
	</body>
</html>

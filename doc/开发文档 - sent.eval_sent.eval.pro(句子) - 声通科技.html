<!DOCTYPE html>
<html lang="en">
	<head>
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
		<meta charset="utf-8" />
		<title>开发文档 - sent.eval&#47;sent.eval.pro(句子)
 - 声通科技</title>

		<meta name="description" content="overview &amp; stats" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />

		<link rel="shortcut icon" href="/favicon.png">

		<!-- bootstrap & fontawesome -->
		<link rel="stylesheet" href="/assets/css/bootstrap.min.css" />
		<link rel="stylesheet" href="/components/font-awesome/css/font-awesome.css" />

		<!-- page specific plugin styles -->
		<link rel="stylesheet" href="/components/jquery.gritter/css/jquery.gritter.min.css" />

		<!-- text fonts -->
		<link rel="stylesheet" href="/assets/css/ace-fonts.min.css" />

		<!-- ace styles -->
		<link rel="stylesheet" href="/assets/css/ace.min.css" class="ace-main-stylesheet" id="main-ace-style" />

		<!--[if lte IE 9]>
			<link rel="stylesheet" href="assets/css/ace-part2.css" class="ace-main-stylesheet" />
		<![endif]-->
		<!--<link rel="stylesheet" href="/assets/css/ace-skins.css" />-->

		<!--[if lte IE 9]>
		  <link rel="stylesheet" href="/assets/css/ace-ie.css" />
		<![endif]-->

		<!-- inline styles related to this page -->


		<!-- HTML5shiv and Respond.js for IE8 to support HTML5 elements and media queries -->

		<!--[if lte IE 8]>
		<script src="/components/html5shiv/dist/html5shiv.min.js"></script>
		<script src="/components/respond/dest/respond.min.js"></script>
        <![endif]-->
        <link rel="stylesheet" href="/assets/css/app.css" />
        
	</head>

	<body class="no-skin">
		<!-- #section:basics/navbar.layout -->
		<div id="navbar" class="navbar navbar-default ace-save-state navbar-fixed-top">
			<div class="navbar-container ace-save-state" id="navbar-container">
				<!-- #section:basics/sidebar.mobile.toggle -->
				
				<button type="button" class="navbar-toggle menu-toggler pull-left" id="menu-toggler" data-target="#sidebar">
					<span class="sr-only">Toggle sidebar</span>

					<span class="icon-bar"></span>

					<span class="icon-bar"></span>

					<span class="icon-bar"></span>
				</button>
				

				<!-- /section:basics/sidebar.mobile.toggle -->
				<div class="navbar-header pull-left">
					<!-- #section:basics/navbar.layout.brand -->
					<a href="/" class="navbar-brand">
						<small>
							<!--<i class="fa fa-leaf"></i>-->
							声通科技
						</small>
					</a>
					<!-- /section:basics/navbar.layout.brand -->

					<!-- #section:basics/navbar.toggle -->

					<!-- /section:basics/navbar.toggle -->
				</div>

				<!-- #section:basics/navbar.dropdown -->
				<div class="navbar-buttons navbar-header pull-right" role="navigation">
					<ul class="nav ace-nav">
						<li class="light-blue li-nav-search">
							<div class="nav-search" id="nav-search" style="top:5px;">
								<form class="form-search">
									<span class="input-icon input-icon-right">
										<input type="text" placeholder="搜索" class="nav-search-input" style="width: 220px;height: 35px !important;color: #deebff !important;background: rgba(9,30,66,0.48);" data-action="nav-search-input" autocomplete="off">
										<i class="ace-icon fa fa-search nav-search-icon" style="top: 4px;"></i>
									</span>
								</form>
							</div>
						</li>
						
						<!-- #section:basics/navbar.user_menu -->
						<li class="light-blue dropdown-modal" style="float: right !important;">
							<a data-toggle="dropdown" href="#" class="dropdown-toggle">
								<!--<img class="nav-user-photo" src="/assets/avatars/user.jpg" alt="Jason's Photo" />-->
								<span class="user-info">
									<small>Welcome,</small>
									<span>seedtu</span>
								</span>
								<i class="ace-icon fa fa-caret-down"></i>
							</a>
							<ul class="user-menu dropdown-menu-right dropdown-menu dropdown-yellow dropdown-caret dropdown-close">
								<!-- <li>
									<a href="#">
										<i class="ace-icon fa fa-cog"></i>
										设置
									</a>
								</li> -->

								<li>
									<a data-id="634" data-name="seedtu" data-email="<EMAIL>"  data-action='edit-user'>
										<i class="ace-icon fa fa-user"></i>
										修改
									</a>
								</li>

								<li class="divider"></li>

								<li>
									<a href="/account/logout">
										<i class="ace-icon fa fa-power-off"></i>
										登出
									</a>
								</li>
							</ul>
						</li>

						<!-- /section:basics/navbar.user_menu -->
					</ul>
				</div>

				<!-- /section:basics/navbar.dropdown -->
			</div><!-- /.navbar-container -->
		</div>

		<!-- /section:basics/navbar.layout -->
		<div class="main-container ace-save-state" id="main-container">
			<script type="text/javascript">
				//try{ace.settings.loadState('main-container')}catch(e){}
			</script>

			<!-- #section:basics/sidebar -->
			<div id="sidebar" class="sidebar responsive ace-save-state sidebar-fixed sidebar-scroll">
				<ul class="nav nav-list" style="top: 0px;"><li class=""><a href="/index"><span class="menu-text"> 首页 </span></a><b class="arrow"></b></li><li class=""><a href="#" class="dropdown-toggle"><span class="menu-text">系统管理</span><b class="arrow fa fa-angle-down"></b></a><b class="arrow"></b><ul class="submenu"><li class=""><a href="/mail_manager/index"><span>关注邮箱管理</span></a><b class="arrow"></b></li></ul></li><li class="active open"><a href="#" class="dropdown-toggle"><span class="menu-text">文档中心</span><b class="arrow fa fa-angle-down"></b></a><b class="arrow"></b><ul class="submenu"><li class=""><a href="#" class="dropdown-toggle"><span>SDK文档</span><b class="arrow fa fa-angle-down"></b></a><b class="arrow"></b><ul class="submenu"><li class=""><a href="#" class="dropdown-toggle"><span>Flutter SDK</span><b class="arrow fa fa-angle-down"></b></a><b class="arrow"></b><ul class="submenu"><li class=""><a href="/docs/index?id=267"><span>接入指南</span></a><b class="arrow"></b></li><li class=""><a href="#" class="dropdown-toggle"><span>开发指南</span><b class="arrow fa fa-angle-down"></b></a><b class="arrow"></b><ul class="submenu"><li class=""><a href="/docs/index?id=268"><span>相关类说明</span></a><b class="arrow"></b></li><li class=""><a href="/docs/index?id=269"><span>FlutterPluginSTkouyu</span></a><b class="arrow"></b></li><li class=""><a href="/docs/index?id=271"><span>KYEngineSetting</span></a><b class="arrow"></b></li><li class=""><a href="/docs/index?id=274"><span>KYRecordSetting</span></a><b class="arrow"></b></li><li class=""><a href="/docs/index?id=276"><span>OnPlayListener</span></a><b class="arrow"></b></li><li class=""><a href="/docs/index?id=275"><span>OnInitEngineListener</span></a><b class="arrow"></b></li><li class=""><a href="/docs/index?id=277"><span>OnRecordListener</span></a><b class="arrow"></b></li><li class=""><a href="/docs/index?id=272"><span>KYEngineType</span></a><b class="arrow"></b></li><li class=""><a href="/docs/index?id=270"><span>KYAudioType</span></a><b class="arrow"></b></li><li class=""><a href="/docs/index?id=280"><span>KYEngineStatus</span></a><b class="arrow"></b></li></ul></li></ul></li></ul></li><li class="active open"><a href="#" class="dropdown-toggle"><span>评测参数及结果说明</span><b class="arrow fa fa-angle-down"></b></a><b class="arrow"></b><ul class="submenu"><li class="active open"><a href="#" class="dropdown-toggle"><span>英文</span><b class="arrow fa fa-angle-down"></b></a><b class="arrow"></b><ul class="submenu"><li class=""><a href="/docs/index?id=48"><span>word.eval/word.eval.pro(单词)</span></a><b class="arrow"></b></li><li class="active"><a href="/docs/index?id=117"><span>sent.eval/sent.eval.pro(句子)</span></a><b class="arrow"></b></li><li class=""><a href="/docs/index?id=118"><span>para.eval(段落)</span></a><b class="arrow"></b></li></ul></li></ul></li><li class=""><a href="#" class="dropdown-toggle"><span>错误码</span><b class="arrow fa fa-angle-down"></b></a><b class="arrow"></b><ul class="submenu"><li class=""><a href="/docs/index?id=26"><span>通用SDK错误码</span></a><b class="arrow"></b></li><li class=""><a href="/docs/index?id=25"><span>服务端错误码</span></a><b class="arrow"></b></li></ul></li></ul></li><li class=""><a href="#" class="dropdown-toggle"><span class="menu-text">下载中心</span><b class="arrow fa fa-angle-down"></b></a><b class="arrow"></b><ul class="submenu"><li class=""><a href="#" class="dropdown-toggle"><span>SDK下载</span><b class="arrow fa fa-angle-down"></b></a><b class="arrow"></b><ul class="submenu"><li class=""><a href="/sdks/index?id=89"><span>通用SDK(3.0)</span></a><b class="arrow"></b></li></ul></li><li class=""><a href="#" class="dropdown-toggle"><span>demo下载</span><b class="arrow fa fa-angle-down"></b></a><b class="arrow"></b><ul class="submenu"><li class=""><a href="/sdks/index?id=103"><span>flutter_demo</span></a><b class="arrow"></b></li></ul></li></ul></li></ul>
				</ul><!-- /.nav-list -->

				<!-- /section:basics/sidebar.layout.minimize -->
			</div>
			<div class="ia-splitter-handle" title="双击还原间距"><div class="ia-splitter-handle-highlight splitter-icon-grab-handle"></div></div>
            

<div class="main-content">
    <div class="main-content-inner">
        <!-- #section:basics/content.breadcrumbs -->
        <div class="breadcrumbs ace-save-state adj" id="breadcrumbs">
            <ul class="breadcrumb">
                <li>
                    <i class="ace-icon fa fa-home home-icon"></i>
                    <a href="/">首页</a>
                </li>
                <li>开发文档</li>
                <li>评测参数及结果说明</li><li>英文</li>
                <li class="active">sent.eval&#47;sent.eval.pro(句子)</li>
            </ul><!-- /.breadcrumb -->
            <div class="nav-search">

                <button class="btn btn-link" data-action='favourite' data-id='117'>关注</button>






            </div>
        </div>

        <!-- /section:basics/content.breadcrumbs -->
        <div class="page-content" style="background-color: #FFF !important">
            <!-- <div class="page-header">
                <div class="row">
                    <div class="col-md-12">
                        
                    </div>
                </div>
            </div> --><!-- /.page-header -->
            <div>
                <div class="row" style="margin-left:0px;margin-right:0px;">
                    <div class="col-md-10 content ck-content" id='content' style="padding-left:40px;padding-top: 24px;">

                            <h2><span style="color:rgb(0,0,0);"><strong>request参数</strong></span></h2><figure class="table"><table><tbody><tr><td style="text-align:center;"><span style="color:rgb(0,0,0);">字段</span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);">类型</span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);">是否必须</span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);">默认值</span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);">说明</span></td></tr><tr><td><span style="color:rgb(0,0,0);">coreType</span></td><td><span style="color:rgb(0,0,0);">字符串</span></td><td><span style="color:rgb(0,0,0);">是</span></td><td>&nbsp;</td><td><p><span style="color:rgb(0,0,0);">sent.eval：英文句子评测，sent.eval.pro：英文句子自适应年龄段评测</span></p><p><span style="color:hsl(0,75%,60%);"><strong>注意：允许最大录音时长 90s</strong></span></p></td></tr><tr><td><span style="color:rgb(0,0,0);">getParam</span></td><td><span style="color:rgb(0,0,0);">整型</span></td><td><span style="color:rgb(0,0,0);">否</span></td><td>1</td><td><span style="color:rgb(0,0,0);">返回结果是否含请求参数。可选值：1、0，1为开启，0为关闭</span></td></tr><tr><td><span style="color:rgb(0,0,0);">refText</span></td><td><span style="color:rgb(0,0,0);">字符串</span></td><td><span style="color:rgb(0,0,0);">是</span></td><td>&nbsp;</td><td><p><span style="color:rgb(0,0,0);">评测文本，</span><span style="color:hsl(0,75%,60%);">不超过 200 个单词或音标，音标格式：/æ/</span></p><p><span style="color:rgb(0,0,0);">支持任意音标评测，音标需与dict_type设置的类型一致（若dict_type设置为IPA88，refText需要传IPA88音标评测）</span></p><p><span style="color:rgb(0,0,0);">1. 48个元辅音 ，如： /</span><span style="color:rgb(57,57,57);">æ/、/p/</span></p><p><span style="color:rgb(57,57,57);">2. 48个元辅音的任意组合，如：/kl/、/suː/</span></p><p><span style="color:rgb(57,57,57);">3. 单词音标评测，支持多种书写方式 ，如：/ˈwɔːtə(r)/、/ʰwaɪ/、/bɜːʴθdeɪ/</span></p></td></tr><tr><td><span style="color:rgb(0,0,0);">attachAudioUrl</span></td><td><span style="color:rgb(0,0,0);">整型</span></td><td><span style="color:rgb(0,0,0);">否</span></td><td><span style="color:rgb(0,0,0);">0</span></td><td><span style="color:rgb(0,0,0);">返回结果是否含音频下载地址。可选值：1、0，</span><span style="color:hsl(0,75%,60%);">提示：音频保留7天，如需长期保存，建议下载到自己服务器。</span></td></tr><tr><td><span style="color:rgb(0,0,0);">dict_type</span></td><td><span style="color:rgb(0,0,0);">字符串</span></td><td><span style="color:rgb(0,0,0);">否</span></td><td><span style="color:rgb(0,0,0);">KK</span></td><td><span style="color:rgb(0,0,0);">返回音素类型 ，可选值：CMU/KK/IPA88</span></td></tr><tr><td><span style="color:rgb(0,0,0);">phoneme_output</span></td><td><span style="color:rgb(0,0,0);">整型</span></td><td><span style="color:rgb(0,0,0);">否</span></td><td><span style="color:rgb(0,0,0);">1</span></td><td><span style="color:rgb(0,0,0);">返回结果是否含音素维度，可选值：1、0，1为开启，0为关闭</span></td></tr><tr><td><span style="color:rgb(0,0,0);">agegroup</span></td><td><span style="color:rgb(0,0,0);">整型</span></td><td><span style="color:rgb(0,0,0);">否</span></td><td><span style="color:rgb(0,0,0);">3</span></td><td><p><span style="color:rgb(0,0,0);">年龄段支持可选值：1、2、3</span></p><p><span style="color:rgb(0,0,0);">1：3-6years old</span></p><p><span style="color:rgb(0,0,0);">2：6-12years old</span></p><p><span style="color:rgb(0,0,0);">3：&gt;12years old</span></p><p><span style="color:hsl(0,75%,60%);">注意：当coreType为word.eval.pro /sent.eval.pro时，该参数无效</span></p></td></tr><tr><td><span style="color:rgb(0,0,0);">slack</span></td><td><span style="color:rgb(0,0,0);">整型或浮点型</span></td><td><span style="color:rgb(0,0,0);">否</span></td><td><span style="color:rgb(0,0,0);">0</span></td><td><span style="color:rgb(0,0,0);">打分松紧度，取值范围[-1,1]，正数加分负数减分 ，0.1 加分幅度较小，1 加分幅度较大，干预后影响各维度分数</span></td></tr><tr><td><span style="color:rgb(0,0,0);">realtime_feedback</span></td><td><span style="color:rgb(0,0,0);">整型</span></td><td><span style="color:rgb(0,0,0);">否</span></td><td><span style="color:rgb(0,0,0);">0</span></td><td><span style="color:rgb(0,0,0);">支持评测结果实时反馈。 可选值：1、0，1为开启，0为关闭</span></td></tr><tr><td><span style="color:rgb(0,0,0);">scale</span></td><td><span style="color:rgb(0,0,0);">整型或浮点型</span></td><td><span style="color:rgb(0,0,0);">否</span></td><td>100</td><td><span style="color:rgb(0,0,0);">分制 ，取值范围(0,100]，影响各维度分数</span></td></tr><tr><td><span style="color:rgb(0,0,0);">precision</span></td><td><span style="color:rgb(0,0,0);">整型或浮点型</span></td><td><span style="color:rgb(0,0,0);">否</span></td><td><span style="color:rgb(0,0,0);">1</span></td><td><span style="color:rgb(0,0,0);">得分精度，取值范围(0,1]，影响各维度分数</span></td></tr><tr><td><span style="color:rgb(0,0,0);">customized_lexicon</span></td><td><span style="color:rgb(0,0,0);">对象</span></td><td><span style="color:rgb(0,0,0);">否</span></td><td>&nbsp;</td><td><p><span style="color:rgb(0,0,0);">指定特定单词期望发音，使用 CMU音素符号，单词、CMU大小写不敏感。支持多单词、多音标</span></p><p><span style="color:rgb(0,0,0);">如:</span></p><p><span style="color:rgb(0,0,0);">"customized_lexicon":</span></p><p><span style="color:rgb(0,0,0);">{</span></p><p><span style="color:rgb(0,0,0);">&nbsp; "but": [ &nbsp; &nbsp; // 除第2个but，文本中其他but均有【bət，bʌt】2种发音</span></p><p><span style="color:rgb(0,0,0);">&nbsp; &nbsp; &nbsp;["B", "AX","T"],</span></p><p><span style="color:rgb(0,0,0);">&nbsp; &nbsp; &nbsp;["B","AH","T"]</span></p><p><span style="color:rgb(0,0,0);">&nbsp; &nbsp;],</span></p><p><span style="color:rgb(0,0,0);">&nbsp; &nbsp;"but@2": [ &nbsp;// 第2个but，只有【bʌt】1种发音。</span><span style="color:red;">注：@后面跟数字表示文本中的第几个“but”，优先级高于无@符号</span></p><p><span style="color:rgb(0,0,0);">&nbsp; &nbsp; &nbsp; &nbsp;["B","AH","T"]</span></p><p><span style="color:rgb(0,0,0);">&nbsp; &nbsp; ]</span></p><p><span style="color:rgb(0,0,0);">}</span></p></td></tr><tr><td><span style="color:rgb(0,0,0);">customized_pron</span></td><td><span style="color:rgb(0,0,0);">对象</span></td><td><span style="color:rgb(0,0,0);">否</span></td><td>&nbsp;</td><td><p><span style="color:rgb(0,0,0);">指定单词期望发音，可选 KK/IPA88，支持多单词、多音标，音标不需要//括起来，支持多种音标书写方式， 如：wɔːtə(r)、bɜːʳθde</span></p><p><span style="color:rgb(0,0,0);">示例：</span></p><p><span style="color:rgb(0,0,0);">"customized_pron":{</span></p><p><span style="color:rgb(0,0,0);">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;"type": "KK",</span></p><p><span style="color:rgb(0,0,0);">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;"pron":{</span></p><p><span style="color:rgb(0,0,0);">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;"but": ["bət","bʌt"], // 除第2个but，文本中其他but均有【bət，bʌt】2种发音</span></p><p><span style="color:rgb(0,0,0);">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;"but@2": ["bʌt"] // 第2个but，只有【bʌt】1种发音。</span><span style="color:red;">注：@后面跟数字表示文本中的第几个“but”，优先级高于无@符号</span></p><p><span style="color:rgb(0,0,0);">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;}</span></p><p><span style="color:rgb(0,0,0);">}</span></p></td></tr><tr><td><span style="color:rgb(0,0,0);">dict_dialect</span></td><td><span style="color:rgb(0,0,0);">字符串</span></td><td><span style="color:rgb(0,0,0);">否</span></td><td>&nbsp;</td><td><p><span style="color:rgb(0,0,0);">可选值：en_br、en_us。en_br表示限定英式发音评测；en_us表示限定美式发音评测；如果不设置此参数，表示不限定英式、美式发音。</span></p><p><span style="color:rgb(0,0,0);">注：发音/o/不同dict_type、dict_dialect值时，返回的样例：</span></p><p>&nbsp; &nbsp;dict_type &nbsp; &nbsp; | &nbsp; &nbsp; &nbsp;dict_dialect &nbsp; &nbsp; &nbsp;| &nbsp; &nbsp; &nbsp;发音/o/返回示例<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;KK &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;| &nbsp; &nbsp; &nbsp; &nbsp; 不设置 &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;| &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; o<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;IPA88 &nbsp; &nbsp; | &nbsp; &nbsp; &nbsp; &nbsp; 不设置 &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;| &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;əʊ<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;KK &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;| &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;en_us &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;| &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; o<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;KK &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;| &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;en_br &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;| &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; o<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;IPA88 &nbsp; &nbsp; | &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;en_us &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;| &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;oʊ<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;IPA88 &nbsp; &nbsp; | &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;en_br &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;| &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; əʊ<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;不设置 &nbsp; &nbsp;| &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;en_us &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;| &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;o<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;不设置 &nbsp; &nbsp;| &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;en_br &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;| &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;o</p></td></tr><tr><td>readtype_diagnosis</td><td>整型</td><td><span style="color:rgb(0,0,0);">否</span></td><td>0</td><td><p><span style="color:rgb(0,0,0);">显示重复读、漏读。可选值：1、0，1为开启，0为关闭</span></p><p><span style="color:rgb(230,76,76);">注：</span></p><p><span style="color:rgb(230,76,76);">1. readtype_diagnosis设置为1时 ，通过result.words[].readType字段对每个单词朗读情况进行了标识。</span></p><p><span style="color:rgb(230,76,76);">2. readtype_diagnosis设置为1时，重复读、错读时result.words数组内单词个数可能大于refText评测文本单词数。</span></p></td></tr><tr><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;">output_rawtext</td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;"><span style="color:rgb(0,0,0);">整型</span></td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;"><span style="color:rgb(0,0,0);">否</span></td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;"><span style="color:rgb(0,0,0);">0</span></td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;"><span style="color:rgb(0,0,0);">保留特殊符号 。可选值：1、0，1为开启，0为关闭</span></td></tr><tr><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;">blend_phoneme</td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;"><span style="color:rgb(0,0,0);">整型</span></td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;">否</td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;">0</td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;"><p>默认为0，可选值：0、1，0表示关闭，1表示开启</p><p>双元音：/ɪə/、/eə/、/ʊə/<br>其他辅音：/tr/、/ts/、/dr/、/dz/<br>以上音素，设置为1后，会合在一起返回</p><p>示例：</p><p>单词track，音标/træk/，result.words[].phonemes字段：<br>开启前返回的音素划分是：/t/ /r/ /æ/ /k/<br>开启后返回的音素划分是：/tr/ /æ/ /k/</p></td></tr><tr><td>blend_phonics</td><td><span style="color:rgb(0,0,0);">整型</span></td><td>否</td><td>0</td><td><p>默认为0，可选值：0、1，0表示关闭，1表示开启</p><p>双元音：/ɪə/、/eə/、/ʊə/<br>其他辅音：/tr/、/ts/、/dr/、/dz/<br>以上音素，设置为1后，会合在一起返回</p><p>示例：</p><p>单词track，音标/træk/，result.words[].phonics字段：<br>开启前返回的phonics划分是：t→/t/、r→/r/、a→/æ/、ck→/k/<br>开启后返回的phonics划分是：tr→/t/ /r/、a→/æ/、ck→/k/</p></td></tr><tr><td>sentence_need_word_stress</td><td>整型</td><td>否</td><td>0</td><td>显示单词重<span style="color:rgb(0,0,0);">(zhòng)</span>读音节，可选值0、1 ，<span style="color:rgb(0,0,0);">1为开启，0为关闭</span></td></tr><tr><td>enable_digit_by_digit_read</td><td><span style="color:rgb(0,0,0);">整型</span></td><td>否</td><td>0</td><td><p>允许数字拆分成单个数字读，默认为0，可选值：0、1，0表示关闭，1表示开启</p><p>示例：1234 5678 001 &nbsp;可以按照1 2 3 4 5 6 7 8 0 0 1去读</p></td></tr></tbody></table></figure><p>&nbsp;</p><h2><strong>结果示例（有errId返回结果）</strong></h2><pre><code class="language-plaintext">{
    "applicationId": "xxx",
    "dtLastResponse": "2025-06-10 10:03:15:496",
    "eof": 1,
    "errId": xx,
    "error": "xx",
    "params": {
        "app": {
            "applicationId": "xxx",
            "sig": "xxx",
            "timestamp": "1749520994",
            "userId": "xxx"
        },
        "audio": {
            "audioType": "opus",
            "channel": 1,
            "sampleBytes": 2,
            "sampleRate": 16000
        },
        "coreProvideType": "cloud",
        "request": {
            "coreType": "sent.eval",
            "phoneme_output": 1,
            "refText": "你",
            "tokenId": "xxx"
        }
    },
    "recordId": "xxx",      // 注：建议业务层保存，方便排查错误
    "refText": "你",
    "tokenId": "xx"
}
</code></pre><h2>&nbsp;</h2><h2><span style="color:rgb(0,0,0);"><strong>中间结果示例（realtime_feedback 设置开启）</strong></span></h2><p><span style="background-color:rgb(255,255,255);color:rgb(230,77,77);"><strong>注：增量返回用户实际朗读内容（只显示评测文本里的内容）</strong></span></p><p><span style="background-color:rgb(255,255,255);color:rgb(230,77,77);"><strong>&nbsp; &nbsp; &nbsp; &nbsp;当返回结果出现errId字段时，无中间结果</strong></span></p><pre><code class="language-plaintext">&nbsp;{
    "timestamp": "2020-12-14 14:26:25:377",            //时间戳信息
    "eof": 0,                                             //结果标识 0:返回中间结果；1:返回最终结果
    "result": {                                           //结果集信息
        "overall": 56,
        "resource_version": "2.8.7",
        "kernel_version": "4.9.4",
        "pronunciation": 56,
        "words": [{
                "word": "Today",
                "word_parts": [{
                        "part": "Today",
                        "charType": 0,
                        "endIndex": 4,
                        "beginIndex": 0
                    }
                ],
                "scores": {
                    "pronunciation": 88
                },
                "charType": 0
            }, {
                "word": "is",
                "word_parts": [{
                        "part": "is",
                        "charType": 0,
                        "endIndex": 7,
                        "beginIndex": 6
                    }
                ],
                "scores": {
                    "pronunciation": 69
                },
                "charType": 0
            }, {
                "word": "a",
                "word_parts": [{
                        "part": "a",
                        "charType": 0,
                        "endIndex": 9,
                        "beginIndex": 9
                    }
                ],
                "scores": {
                    "pronunciation": 99
                },
                "charType": 0
            }, {
                "word": "fine",
                "word_parts": [{
                        "part": "fine",
                        "charType": 0,
                        "endIndex": 14,
                        "beginIndex": 11
                    }
                ],
                "scores": {
                    "pronunciation": 17
                },
                "charType": 0
            }, {
                "word": "day.",
                "word_parts": [{
                        "part": "day",
                        "charType": 0,
                        "endIndex": 18,
                        "beginIndex": 16
                    }, {
                        "part": ".",
                        "charType": 1
                    }
                ],
                "scores": {
                    "pronunciation": 7
                },
                "charType": 0
            }
        ]
    },
    "recordId": "xxx"                              //评分唯一id  注：建议业务层保存，方便排查错误
}
</code></pre><h3>&nbsp;</h3><h2><span style="color:rgb(0,0,0);"><strong>中间结果字段说明（中间结果 realtime_feedback &nbsp;设置开启后返回）</strong></span></h2><p><span style="background-color:rgb(255,255,255);color:rgb(230,77,77);"><strong>注：当返回结果出现errId字段时，无中间结果</strong></span></p><figure class="table"><table><tbody><tr><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>属性</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>类型</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>含义</strong></span></td></tr><tr><td><span style="color:rgb(0,0,0);">timestamp &nbsp;</span></td><td><span style="color:rgb(0,0,0);">字符串</span></td><td><span style="color:rgb(0,0,0);">当前时间戳</span></td></tr><tr><td><span style="color:rgb(0,0,0);">eof</span></td><td>整型</td><td><span style="color:rgb(0,0,0);">0：中间结果；1：最终结果</span></td></tr><tr><td><span style="color:rgb(0,0,0);">recordId</span></td><td><span style="color:rgb(0,0,0);">字符串</span></td><td><span style="color:rgb(0,0,0);">评分唯一 id（同最终结果 recordId） </span><span style="color:hsl(0, 75%, 60%);">注：建议业务层保存，方便排查错误</span></td></tr><tr><td><span style="color:rgb(0,0,0);">result</span></td><td>对象</td><td><span style="color:rgb(0,0,0);">评分结果</span></td></tr></tbody></table></figure><p>&nbsp;</p><p><span style="color:rgb(0,0,0);"><strong>result</strong></span></p><figure class="table"><table><tbody><tr><td style="text-align:center;"><strong>属性</strong></td><td style="text-align:center;"><strong>类型</strong></td><td style="text-align:center;"><strong>含义</strong></td></tr><tr><td><span style="color:rgb(0,0,0);">words</span></td><td><span style="color:rgb(0,0,0);">对象数组</span></td><td>单词评分结果</td></tr><tr><td><span style="color:rgb(0,0,0);">kernel_version</span></td><td><span style="color:rgb(51,51,51);">字符串</span></td><td><span style="color:rgb(0,0,0);">内核版本</span></td></tr><tr><td><span style="color:rgb(0,0,0);">pronunciation</span></td><td>整型或浮点型</td><td><span style="color:rgb(0,0,0);">发音得分。</span><span style="color:rgb(230,76,76);">若 precision（精度）设置为浮点型时，该值显示为浮点型（若该值小数 点后为 0，则显示成整型），若 scale（分 制）设置为（0,1）, 该值显示为浮点型， 否则为整型。</span></td></tr><tr><td><span style="color:rgb(0,0,0);">overall</span></td><td>整型或浮点型</td><td><span style="color:rgb(0,0,0);">总分。</span><span style="color:rgb(230,76,76);">若 precision（精度）设置为浮点型时，该值显示为浮点型（若该值小数 点后为 0，则显示成整型），若 scale（分 制）设置为（0,1）, 该值显示为浮点型， 否则为整型。</span></td></tr><tr><td><span style="color:rgb(0,0,0);">resource_version</span></td><td><span style="color:rgb(51,51,51);">字符串</span></td><td><span style="color:rgb(0,0,0);">资源版本</span></td></tr></tbody></table></figure><p>&nbsp;</p><p><span style="color:rgb(0,0,0);"><strong>result- &gt;&nbsp;words</strong></span></p><figure class="table"><table><tbody><tr><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>属性</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>类型</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>含义</strong></span></td></tr><tr><td><span style="color:rgb(0,0,0);">charType&nbsp;</span></td><td>整型</td><td><span style="color:rgb(0,0,0);">0:：非标点符号，1：标点符号</span></td></tr><tr><td><span style="color:rgb(0,0,0);">scores</span></td><td><span style="color:rgb(0,0,0);">对象</span></td><td><span style="color:rgb(0,0,0);">得分详情</span></td></tr><tr><td><span style="color:rgb(0,0,0);">word</span></td><td><span style="color:rgb(0,0,0);">字符串</span></td><td><span style="color:rgb(0,0,0);">评测文本中的单词</span></td></tr><tr><td><span style="color:rgb(0,0,0);">word_parts</span></td><td><span style="color:rgb(0,0,0);">对象数组</span></td><td><span style="color:rgb(0,0,0);">单词文本信息</span></td></tr></tbody></table></figure><p>&nbsp;</p><p><span style="color:rgb(0,0,0);"><strong>result- &gt;&nbsp;words- - &gt;scores</strong></span></p><figure class="table"><table><tbody><tr><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>属性</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>类型</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>含义</strong></span></td></tr><tr><td><span style="color:rgb(0,0,0);">pronunciation</span></td><td><span style="color:rgb(0,0,0);">整型或浮点型</span></td><td><span style="color:rgb(0,0,0);">单词发音得分。</span><span style="color:rgb(230,76,76);">若 precision（精度）设置为浮点型时，该值显示为浮点型（若该值小数 点后为 0,则显示成整型），若 scale（分 制）设置为（0,1）, 该值显示为浮点型， 否则为整型。</span></td></tr></tbody></table></figure><p>&nbsp;</p><p><span style="color:rgb(0,0,0);"><strong>result- &gt;&nbsp;words- - &gt;word_parts</strong></span></p><figure class="table"><table><tbody><tr><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>属性</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>类型</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>含义</strong></span></td></tr><tr><td><span style="color:rgb(0,0,0);">charType&nbsp;</span></td><td>整型</td><td><span style="color:rgb(0,0,0);">0：非标点符号，1：标点符号</span></td></tr><tr><td><span style="color:rgb(0,0,0);">part&nbsp;</span></td><td><span style="color:rgb(0,0,0);">字符串</span></td><td><span style="color:rgb(0,0,0);">文本</span></td></tr><tr><td>endIndex</td><td>整型</td><td>单词在文本中结束的位置</td></tr><tr><td>beginIndex</td><td>整型</td><td>单词在文本中开始的位置</td></tr></tbody></table></figure><p>&nbsp;</p><h2><strong>最终结果示例</strong></h2><pre><code class="language-plaintext">{
  "tokenId": "abcd",
  "params": {
    "app": {
      "timestamp": "1713837214",
      "userId": "user_id",
      "applicationId": "xxxx",
      "sig": "d5bb2e22abd71418f2d06ddd6e3238fdeddb9f32"
    },
    "audio": {
      "sampleBytes": 2,
      "audioType": "mp3",
      "sampleRate": 16000,
      "channel": 1
    },
    "request": {
      "tokenId": "abcd",
      "coreType": "sent.eval.pro",
      "readtype_diagnosis": 1,
      "sentence_need_word_stress":1,
      "refText": "It is an old book"
    }
  },
  "applicationId": "xxxx",
  "refText": "It is an old book",
  "dtLastResponse": "2024-04-23 09:53:34:274",
  "result": {
    "overall": 49,
    "speed": 104,
    "resource_version": "4.0.4",
    "pronunciation": 49,
	"warning": [
      {
        "code": 1004,
        "message": "Audio noisy!"
      }
    ],
    "liaison": [ // 实际连读才会出现该字段，需判断该字段是否存在
      {
        "second": {
          "word": "is",
          "index": 1
        },
        "first": {
          "word": "It",
          "index": 0
        },
        "linkable_type": 0,
        "first_phoneme": "t",
        "second_phoneme": "ɪ"
      }
    ],
    "plosion": [ // 实际爆破才会出现该字段，需判断该字段是否存在
       {
        "second": {
          "word": "book",
          "index": 4
        },
        "first": {
          "word": "old",
          "index": 3
        },
        "linkable_type": 3,
         "first_phoneme": "d",
        "second_phoneme": "b"
      }
    ],
    "words": [
      {
        "pause": {
          "type": 0,
          "duration": 0
        },
        "linkable": 1,
        "linkable_type": 0,
        "linked": 1,
        "phonemes": [
          {
            "phoneme": "ɪ",
            "span": {
              "end": 142,
              "start": 122
            },
            "pronunciation": 97,
            "stress_mark": 0          // 该音素前的重音符号信息。0表示该音素前无重音符号、1表示该音素前有重音符号（ˈ）、2表示该音素前有次重音符号（ˌ）
          },
          {
            "phoneme": "t",
            "span": {
              "end": 153,
              "start": 142
            },
            "pronunciation": 100,
            "stress_mark": 0 
          }
        ],
        "span": {
          "end": 153,
          "start": 122
        },
        "charType": 0,
        "scores": {
          "stress": [
            {
              "phoneme_offset": 0,
              "phonetic": "ɪt",
              "ref_stress": 1,
              "spell": "It",
              "overall": 99,
              "stress": 1
            }
          ],
          "prominence": 0,
          "overall": 99,
          "pronunciation": 99
        },
        "word_parts": [
          {
            "beginIndex": 0,
            "part": "It",
            "charType": 0,
            "endIndex": 1
          }
        ],
        "word": "It",
        "readType": 0,
        "phonics": [		//未划分字母、音标对应关系时，phonics字段返回示例
          {
            "spell": "It",	//字母拼写
            "phoneme": [	//字母音素信息集
              "ɪ",
              "t" 
            ],
            "overall": 97	//字母得分
          }
       ],
        "phonics": [		//已划分字母、音标对应关系时，phonics字段返回示例
          {
            "spell": "I",	//字母拼写
            "phoneme": [	//字母音素信息集
              "ɪ"
            ],
            "overall": 97	//字母得分
          },
          {
            "spell": "t",
            "phoneme": [
              "t"
            ],
            "overall": 100
          }
        ]
      },
      {
        "pause": {
          "type": 1,
          "duration": 16
        },
        "linkable": 1,
        "linked": 0,
        "linkable_type": 0,
        "phonemes": [
          {
            "phoneme": "ɪ",
            "span": {
              "end": 185,
              "start": 153
            },
            "pronunciation": 98,
            "stress_mark": 0 
          },
          {
            "phoneme": "z",
            "span": {
              "end": 212,
              "start": 185
            },
            "pronunciation": 79,
            "stress_mark": 0  
          }
        ],
        "span": {
          "end": 212,
          "start": 153
        },
        "charType": 0,
        "scores": {
        	"stress": [
            {
              "phoneme_offset": 0,
              "phonetic": "ɪz",
              "ref_stress": 1,
              "spell": "is",
              "overall": 90,
              "stress": 1
            }
          ],
          "prominence": 0,
          "overall": 90,
          "pronunciation": 90
        },
        "word_parts": [
          {
            "beginIndex": 3,
            "part": "is",
            "charType": 0,
            "endIndex": 4
          }
        ],
        "word": "is",
        "readType": 0,
        "phonics": [
          {
            "spell": "i",
            "phoneme": [
              "ɪ"
            ],
            "overall": 98
          },
          {
            "spell": "s",
            "phoneme": [
              "z"
            ],
            "overall": 79
          }
        ]
      },
      {
        "pause": {
          "type": 1,
          "duration": 37
        },
        "linkable": 1,
        "linked": 0,
        "linkable_type": 0,
        "phonemes": [
          {
            "phoneme": "ə",
            "span": {
              "end": 231,
              "start": 228
            },
            "pronunciation": 0,
            "stress_mark": 0 
          },
          {
            "phoneme": "n",
            "span": {
              "end": 240,
              "start": 231
            },
            "pronunciation": 4,
            "stress_mark": 0 
          }
        ],
        "span": {
          "end": 240,
          "start": 228
        },
        "charType": 0,
        "scores": {
          "prominence": 0,
          "stress": [
            {
              "phoneme_offset": 0,
              "phonetic": "ən",
              "ref_stress": 1,
              "spell": "an",
              "overall": 1,
              "stress": 0
            }
          ],
          "overall": 1,
          "pronunciation": 1
        },
        "word_parts": [
          {
            "beginIndex": 6,
            "part": "an",
            "charType": 0,
            "endIndex": 7
          }
        ],
        "word": "an",
        "readType": 3,
        "phonics": [
          {
            "spell": "a",
            "phoneme": [
              "ə"
            ],
            "overall": 0
          },
          {
            "spell": "n",
            "phoneme": [
              "n"
            ],
            "overall": 4
          }
        ]
      },
      {
        "pause": {
          "type": 1,
          "duration": 41
        },
        "linkable": 1,
        "linked": 1,
        "linkable_type": 3,
        "phonemes": [
          {
            "phoneme": "o",
            "span": {
              "end": 300,
              "start": 277
            },
            "pronunciation": 7,
            "stress_mark": 0
          },
          {
            "phoneme": "l",
            "span": {
              "end": 304,
              "start": 300
            },
            "pronunciation": 33,
            "stress_mark": 0
          },
          {
            "phoneme": "d",
            "span": {
              "end": 325,
              "start": 304
            },
            "pronunciation": 98,
            "stress_mark": 0
          }
        ],
       
        "span": {
          "end": 325,
          "start": 277
        },
        "charType": 0,
        "scores": {
          "prominence": 0,
          "stress": [
            {
              "phoneme_offset": 0,
              "phonetic": "old",
              "ref_stress": 1,
              "spell": "old",
              "overall": 51,
              "stress": 0
            }
          ],
          "overall": 51,
          "pronunciation": 51
        },
        "word_parts": [
          {
            "beginIndex": 9,
            "part": "old",
            "charType": 0,
            "endIndex": 11
          }
        ],
        "word": "old",
        "readType": 0,
        "phonics": [
          {
            "spell": "o",
            "phoneme": [
              "o"
            ],
            "overall": 7
          },
          {
            "spell": "l",
            "phoneme": [
              "l"
            ],
            "overall": 33
          },
          {
            "spell": "d",
            "phoneme": [
              "d"
            ],
            "overall": 98
          }
        ]
      },
      {
        "charType": 0,
        "readType": 4,
        "word": "old",
        "scores": {
          "prominence": 0,
          "overall": 78,
          "pronunciation": 78
        }
      },
      {
        "pause": {
          "type": 0,
          "duration": 0
        },
        "linkable": 0,
        "linked": 0,
        "linkable_type": -1,
        "phonemes": [
          {
            "phoneme": "b",
            "span": {
              "end": 377,
              "start": 366
            },
            "pronunciation": 0,
            "stress_mark": 0 
          },
          {
            "phoneme": "ʊ",
            "span": {
              "end": 380,
              "start": 377
            },
            "pronunciation": 0,
            "stress_mark": 0 
          },
          {
            "phoneme": "k",
            "span": {
              "end": 383,
              "start": 380
            },
            "pronunciation": 0,
            "stress_mark": 0 
          }
        ],
       
        "span": {
          "end": 383,
          "start": 366
        },
        "charType": 0,
        "scores": {
          "prominence": 0,
          "stress": [
            {
              "phoneme_offset": 0,
              "phonetic": "bʊk",
              "ref_stress": 1,
              "spell": "book",
              "overall": 0,
              "stress": 0
            }
          ],
          "overall": 0,
          "pronunciation": 0
        },
        "word_parts": [
          {
            "beginIndex": 13,
            "part": "book",
            "charType": 0,
            "endIndex": 16
          }
        ],
        "word": "book",
        "readType": 3,
        "phonics": [
          {
            "spell": "b",
            "phoneme": [
              "b"
            ],
            "overall": 0
          },
          {
            "spell": "oo",
            "phoneme": [
              "ʊ"
            ],
            "overall": 0
          },
          {
            "spell": "k",
            "phoneme": [
              "k"
            ],
            "overall": 0
          }
        ]
      }
    ],
    "integrity": 100,
    "kernel_version": "6.4.2",
    "fluency": 64,
    "rhythm": 58,
    "duration": "4.356",
    "numeric_duration":	4.356,
    "pause_count": 3,
    "rear_tone": "fall"
  },
  "recordId": "xxxxx",              // 注：建议业务层保存，方便排查错误
  "eof": 1
}</code></pre><h3>&nbsp;</h3><h2><span style="color:rgb(0,0,0);"><strong>最终结果字段说明</strong></span></h2><figure class="table"><table><tbody><tr><td style="text-align:center;"><strong>属性</strong></td><td style="text-align:center;"><strong>类型</strong></td><td style="text-align:center;"><strong>含义</strong></td></tr><tr><td><span style="color:rgb(0,0,0);">tokenId</span></td><td><span style="color:rgb(0,0,0);">字符串</span></td><td><span style="color:rgb(0,0,0);">终端用户请求 id</span></td></tr><tr><td>refText</td><td>字符串</td><td>评测文本</td></tr><tr><td>audioUrl</td><td><span style="color:rgb(51,51,51);">字符串</span></td><td><span style="color:rgb(0,0,0);">在线音频地址</span><span style="color:hsl(0,75%,60%);">，需设置attachAudioUrl</span></td></tr><tr><td><span style="color:rgb(0,0,0);">dtLastResponse</span></td><td>字符串</td><td><span style="color:rgb(51,51,51);">评分结果返回时间</span></td></tr><tr><td><span style="color:rgb(0,0,0);">result</span></td><td><span style="color:rgb(51,51,51);">对象</span></td><td><span style="color:rgb(51,51,51);">评分结果</span></td></tr><tr><td><span style="color:rgb(0,0,0);">applicationId</span></td><td><span style="color:rgb(51,51,51);">字符串</span></td><td><span style="color:rgb(0,0,0);">appKey</span></td></tr><tr><td>recordId</td><td>字符串</td><td>评分唯一 id &nbsp;&nbsp;<span style="color:hsl(0,75%,60%);">注：建议业务层保存，方便排查错误</span></td></tr><tr><td>params</td><td>对象</td><td>请求参数，<span style="color:hsl(0,75%,60%);">需设置getParam</span></td></tr><tr><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;">errId</td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;"><span style="color:rgb(0,0,0);">整型</span></td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;">错误码 &nbsp; &nbsp;<span style="color:rgb(230,77,77);">注：当出现该字段时，返回结果无result字段</span></td></tr><tr><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;">error</td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;"><span style="color:rgb(0,0,0);">字符串</span></td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;">错误码信息 &nbsp;&nbsp;<span style="color:rgb(230,77,77);"> 注：当出现该字段时，返回结果无result字段</span></td></tr></tbody></table></figure><p>&nbsp;</p><h4><span style="color:rgb(0,0,0);"><strong>result</strong></span></h4><p><span style="background-color:rgb(255,255,255);color:rgb(230,77,77);">注：当返回结果出现errId字段时，无该字段</span></p><figure class="table"><table><tbody><tr><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>属性</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>类型</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>含义</strong></span></td></tr><tr><td><span style="color:rgb(0,0,0);">fluency&nbsp;</span></td><td><span style="color:rgb(0,0,0);">整型或浮点型</span></td><td><span style="color:rgb(0,0,0);">流利度。</span><span style="color:rgb(230,76,76);">若 precision（精度）设置为浮点型时，该值显示为浮点型（若该值小数 点后为 0，则显示成整型），若 scale（分 制）设置为（0,1）, 该值显示为浮点型， 否则为整型。</span></td></tr><tr><td><span style="color:rgb(0,0,0);">words&nbsp;</span></td><td><span style="color:rgb(0,0,0);">对象数组</span></td><td><span style="color:rgb(0,0,0);">各单词得分</span></td></tr><tr><td><span style="color:rgb(0,0,0);">duration</span></td><td><span style="color:rgb(0,0,0);">字符串</span></td><td><span style="color:rgb(0,0,0);">音频时长</span><span style="color:hsl(0,0%,0%);">。单位：秒</span></td></tr><tr><td><span style="color:rgb(0,0,0);">kernel_version</span></td><td><span style="color:rgb(0,0,0);">字符串</span></td><td><span style="color:rgb(0,0,0);">内核版本</span></td></tr><tr><td><span style="color:rgb(0,0,0);">rear_tone</span></td><td><span style="color:rgb(0,0,0);">字符串</span></td><td><span style="color:rgb(0,0,0);">句末语调，实际发音语调；rise：升调，fall：降调</span></td></tr><tr><td><span style="color:rgb(0,0,0);">speed&nbsp;</span></td><td>整型</td><td><span style="color:rgb(0,0,0);">语速。单位：词/分</span></td></tr><tr><td><span style="color:rgb(0,0,0);">integrity</span></td><td><span style="color:rgb(0,0,0);">整型或浮点型</span></td><td><span style="color:rgb(0,0,0);">完整度。</span><span style="color:rgb(230,76,76);">若 precision（精度）设置为浮点型时，该值显示为浮点型（若该值小数 点后为 0，则显示成整型），若 scale（分 制）设置为（0,1）, 该值显示为浮点型， 否则为整型。</span></td></tr><tr><td><span style="color:rgb(0,0,0);">pronunciation</span></td><td><span style="color:rgb(0,0,0);">整型或浮点型</span></td><td><span style="color:rgb(0,0,0);">发音得分。</span><span style="color:rgb(230,76,76);">若 precision（精度）设置为浮点型时，该值显示为浮点型（若该值小数 点后为 0，则显示成整型），若 scale（分 制）设置为（0,1）, 该值显示为浮点型， 否则为整型。</span></td></tr><tr><td>overall&nbsp;</td><td><span style="color:rgb(0,0,0);">整型或浮点型</span></td><td>总分。<span style="color:rgb(230,76,76);">若 precision（精度）设置为浮点型时，该值显示为浮点型（若该值小数 点后为 0，则显示成整型），若 scale（分 制）设置为（0,1）, 该值显示为浮点型， 否则为整型。</span></td></tr><tr><td>resource_version</td><td><span style="color:rgb(0,0,0);">字符串</span></td><td>资源版本</td></tr><tr><td>liaison</td><td>对象数组</td><td>连读检测。<span style="color:hsl(0,75%,60%);">支持连读且实际发连读音，则出现该维度</span></td></tr><tr><td>plosion</td><td>对象数组</td><td>不完全爆破检测。<span style="color:hsl(0,75%,60%);">支持爆破且实际发爆破音，则出现该维度，且仅支持两个词之间的爆破</span></td></tr><tr><td>rhythm&nbsp;</td><td><span style="color:rgb(0,0,0);">整型或浮点型</span></td><td>韵律度得分。<span style="color:rgb(230,76,76);">若 precision（精度）设置为浮点型时，该值显示为浮点型（若该值小数 点后为 0，则显示成整型），若 scale（分 制）设置为（0,1）, 该值显示为浮点型， 否则为整型。</span></td></tr><tr><td>warning&nbsp;</td><td>对象数组</td><td>音频检测的提示。<span style="color:rgb(230,76,76);"> 注：非必现字段，使用前需先判断该字段是否存在，详情请参考：</span><a href="/docs/index?id=25" target="_blank"><span style="color:rgb(230,76,76);">服务端错误码</span></a></td></tr><tr><td>pause_count</td><td>整型</td><td>停顿次数</td></tr><tr><td>numeric_duration</td><td><span style="color:rgb(0,0,0);">整型或浮点型</span></td><td><span style="color:rgb(0,0,0);">音频时长</span><span style="color:hsl(0,0%,0%);">。单位：秒</span></td></tr></tbody></table></figure><p>&nbsp;</p><h4><span style="color:rgb(0,0,0);"><strong>result-&gt;warning（对象数组）</strong></span></h4><figure class="table"><table><tbody><tr><td><strong>属性</strong></td><td><strong>类型</strong></td><td><strong>含义</strong></td></tr><tr><td>code</td><td>整型</td><td>错误码</td></tr><tr><td>message</td><td>字符串</td><td>音频检测的提示信息，请参考<a href="https://doc-api.stkouyu.com/docs/index?id=25" target="_blank">服务端错误码</a></td></tr></tbody></table></figure><p>&nbsp;</p><h4><span style="color:rgb(51,51,51);"><strong>result-&gt;words </strong></span><span style="color:rgb(0,0,0);"><strong>（对象数组）</strong></span></h4><figure class="table"><table><tbody><tr><td style="text-align:center;"><strong>属性</strong></td><td style="text-align:center;"><strong>类型</strong></td><td style="text-align:center;"><strong>含义</strong></td></tr><tr><td><span style="color:rgb(0,0,0);">scores</span></td><td><span style="color:rgb(0,0,0);">对象</span></td><td><span style="color:rgb(0,0,0);">句子中的单词得分情况。</span></td></tr><tr><td>span</td><td><span style="color:rgb(0,0,0);">对象</span></td><td><span style="color:rgb(0,0,0);">单词在音轨上的时间范围</span>。<span style="color:hsl(0,75%,60%);">注：当存在readType字段且值为4时，无该字段</span></td></tr><tr><td>word</td><td><span style="color:rgb(51,51,51);">字符串</span></td><td>评测文本中的单词</td></tr><tr><td><span style="color:rgb(0,0,0);">charType</span></td><td>整型</td><td><span style="color:rgb(51,51,51);">0：非标点符号，1：标点符号 &nbsp;&nbsp;</span><span style="color:rgb(0,0,0);"> </span><span style="color:hsl(0,75%,60%);">注: charType值为1时，words 只显示charType跟word字段</span></td></tr><tr><td>phonics &nbsp;</td><td>对象数组</td><td>发音字母组合。<span style="color:hsl(0,75%,60%);">注：当存在readType字段且值为4时，无该字段</span></td></tr><tr><td><span style="color:rgb(0,0,0);">phonemes</span></td><td><span style="color:rgb(51,51,51);">对象数组</span></td><td><span style="color:rgb(51,51,51);">音素信息， </span><span style="color:rgb(0,0,0);">需要开启 phoneme_output。</span><span style="color:hsl(0,75%,60%);">注：当存在readType字段且值为4时，无该字段</span></td></tr><tr><td><span style="color:rgb(0,0,0);">word_parts &nbsp;</span></td><td>对象数组</td><td>单词文本信息<span style="color:rgb(0,0,0);">。</span><span style="color:hsl(0,75%,60%);">注：当存在readType字段且值为4时，无该字段</span></td></tr><tr><td>readType</td><td>整型</td><td>0为正常，3为漏读 ，<span style="color:rgb(0,0,0);">4为重复读 。</span><span style="color:hsl(0,75%,60%);">注：需开启readtype_diagnosis</span></td></tr><tr><td>pause</td><td>对象数组</td><td>停顿。<span style="color:hsl(0,75%,60%);">注：当存在readType字段且值为4时，无该字段</span></td></tr><tr><td>linkable</td><td>整型</td><td>本单词与下一个单词之间的连读标准，1：连读，0：非连读。<span style="color:hsl(0,75%,60%);">注：当存在readType字段且值为4时，无该字段</span></td></tr><tr><td>linked</td><td>整型</td><td>本单词与下一个单词的实际连读情况，1：连读，0：非连读。<span style="color:hsl(0,75%,60%);">注：当存在readType字段且值为4时，无该字段</span></td></tr><tr><td>linkable_type</td><td>整型</td><td><p>本单词与下一个单词的连读类型。<span style="color:hsl(0,75%,60%);">注：当存在readType字段且值为4时，无该字段</span></p><p>-1：不可连读</p><p>0：辅音 + 元音</p><p>1：该单词的末尾和下一个单词的开始的音素都是同一个辅音</p><p>2：辅音 + 半元音</p><p>3：失去爆破</p><p>4：元音 + 元音</p><p>5：辅音 + him/her</p><p>6：/ɚ/&nbsp;+&nbsp;元音</p><p>7：元音 + him/her</p></td></tr></tbody></table></figure><p>&nbsp;</p><p><span style="color:rgb(0,0,0);"><strong>result- &gt;words- &gt;&nbsp; word_parts</strong></span><span style="color:hsl(0,75%,60%);"><strong>（</strong>当存在readType字段且值为4时，无该字段<strong>）</strong></span></p><figure class="table"><table><tbody><tr><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>属性</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>类型</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>含义</strong></span></td></tr><tr><td>charType</td><td>整型</td><td><span style="color:rgb(0,0,0);">0：非标点符号，1：标点符号</span></td></tr><tr><td><span style="color:rgb(0,0,0);">part&nbsp;</span></td><td><span style="color:rgb(0,0,0);">字符串</span></td><td><span style="color:rgb(0,0,0);">文本</span></td></tr><tr><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;">beginIndex</td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;">整型</td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;">单词在文本中开始的位置</td></tr><tr><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;">endIndex</td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;">整型</td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;">单词在文本中结束的位置</td></tr></tbody></table></figure><p>&nbsp;</p><p><span style="color:rgb(0,0,0);"><strong>result-&gt;words-&gt;scores</strong></span></p><figure class="table"><table><tbody><tr><td style="text-align:center;"><strong>属性</strong></td><td style="text-align:center;"><strong>类型</strong></td><td style="text-align:center;"><strong>含义</strong></td></tr><tr><td><span style="color:rgb(0,0,0);">prominence</span></td><td>整型</td><td><span style="color:rgb(0,0,0);">单词重(zhòng)读，</span><span style="color:rgb(51,51,51);">0 表示非重读，1表示重读 &nbsp;&nbsp;</span><span style="color:hsl(0,75%,60%);"> 注：该重读表示实际声音是轻还是重</span></td></tr><tr><td><span style="color:rgb(0,0,0);">overall</span></td><td>整型或浮点型</td><td><span style="color:rgb(0,0,0);">句子中的单词总分。</span><span style="color:rgb(230,76,76);">若 precision（精度）设置为浮点型时，该值显示为浮点型（若该值小数 点后为 0，则显示成整型），若 scale（分 制）设置为（0,1）, 该值显示为浮点型， 否则为整型。</span></td></tr><tr><td><span style="color:rgb(0,0,0);">pronunciation</span></td><td><span style="color:rgb(0,0,0);">整型或浮点型</span></td><td><span style="color:rgb(0,0,0);">句子中的单词发音得分。</span><span style="color:rgb(230,76,76);">若 precision（精度）设置为浮点型时，该值显示为浮点型（若该值小数 点后为 0，则显示成整型），若 scale（分 制）设置为（0,1）, 该值显示为浮点型， 否则为整型。</span></td></tr><tr><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;"><span style="color:rgb(0,0,0);">stress&nbsp;</span></td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;"><span style="color:rgb(0,0,0);">对象数组</span></td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;"><span style="color:rgb(0,0,0);">重(zhòng)读音节信息 &nbsp;</span><span style="color:hsl(0,75%,60%);">注：当存在readType字段且值为4时，无该字段</span></td></tr></tbody></table></figure><p>&nbsp;</p><p><span style="color:rgb(0,0,0);"><strong>result- - &gt;words- - &gt;scores- -&gt; stress</strong></span><span style="color:hsl(0,75%,60%);"><strong>（</strong>当存在readType字段且值为4时，无该字段<strong>）</strong></span></p><p><span style="color:rgb(230,77,77);"><strong>注：数组为1时表示单音节音标，不建议显示重音符号</strong></span></p><figure class="table"><table><tbody><tr><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;text-align:center;vertical-align:top;"><span style="color:rgb(0,0,0);"><strong>属性</strong></span></td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;text-align:center;vertical-align:top;"><span style="color:rgb(0,0,0);"><strong>类型</strong></span></td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;text-align:center;vertical-align:top;"><span style="color:rgb(0,0,0);"><strong>含义</strong></span></td></tr><tr><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;"><span style="color:rgb(0,0,0);">phonetic</span></td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;"><span style="color:rgb(0,0,0);">字符串</span></td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;"><span style="color:rgb(0,0,0);">本音节对应音标</span></td></tr><tr><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;"><span style="color:rgb(0,0,0);">spell&nbsp;</span></td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;"><span style="color:rgb(0,0,0);">字符串</span></td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;"><span style="color:rgb(0,0,0);">本音节对应的拼写</span></td></tr><tr><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;"><span style="color:rgb(0,0,0);">stress</span></td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;"><span style="color:rgb(0,0,0);">整型</span></td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;"><span style="color:rgb(0,0,0);">本音节的实际重(zhòng)音。1表示重(zhòng)读音节、2表示次重(zhòng)音节、0表示非重(zhòng)读</span></td></tr><tr><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;"><span style="color:rgb(0,0,0);">ref_stress</span></td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;"><span style="color:rgb(0,0,0);">整型</span></td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;"><span style="color:rgb(0,0,0);">本音节的参考重(zhòng)音。1表示重(zhòng)读音节、2表示次重(zhòng)音节、0表示非重(zhòng)读</span></td></tr><tr><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;"><span style="color:rgb(0,0,0);">phoneme_offset</span></td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;"><span style="color:rgb(0,0,0);">整型</span></td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;"><span style="color:rgb(0,0,0);">音节在整个音标的起始位置</span></td></tr><tr><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;"><span style="color:rgb(0,0,0);">overall</span></td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;"><span style="color:rgb(0,0,0);">整型或浮点型</span></td><td style="border-bottom:1px solid rgb(191, 191, 191);border-left:1px solid rgb(191, 191, 191);border-right:1px solid rgb(191, 191, 191);border-top:1px solid rgb(191, 191, 191);padding:0.4em;vertical-align:top;"><span style="color:rgb(0,0,0);">音节总分。</span><span style="color:rgb(230,76,76);">若 precision（精度）设置为浮点型时，该值显示为浮点型（若该值小数 点后为 0,则显示成整型），若 scale（分 制）设置为（0,1）, 该值显示为浮点型， 否则为整型。</span></td></tr></tbody></table></figure><p>&nbsp;</p><p><span style="color:rgb(0,0,0);"><strong>result-&gt;words-&gt;span</strong></span><span style="color:hsl(0,75%,60%);"><strong>（</strong>当存在readType字段且值为4时，无该字段<strong>）</strong></span></p><figure class="table"><table><tbody><tr><td style="text-align:center;"><strong>属性</strong></td><td style="text-align:center;"><strong>类型</strong></td><td style="text-align:center;"><strong>含义</strong></td></tr><tr><td><span style="color:rgb(0,0,0);">end</span></td><td>整型</td><td><span style="color:rgb(0,0,0);">单词在音轨上的结束时间，单位：10毫秒</span></td></tr><tr><td><span style="color:rgb(0,0,0);">start&nbsp;</span></td><td>整型</td><td><span style="color:rgb(0,0,0);">单词在音轨上的开始时间，单位：10毫秒</span></td></tr></tbody></table></figure><p>&nbsp;</p><p><span style="color:rgb(0,0,0);"><strong>result-&gt;words-&gt;phonemes</strong></span><span style="color:hsl(0,75%,60%);"><strong>（</strong>当存在readType字段且值为4时，无该字段<strong>）</strong></span></p><figure class="table"><table><tbody><tr><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>属性</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>类型</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>含义</strong></span></td></tr><tr><td><span style="color:rgb(0,0,0);">span</span></td><td><span style="color:rgb(0,0,0);">对象</span></td><td><span style="color:rgb(0,0,0);">音素在音轨上的时间</span></td></tr><tr><td><span style="color:rgb(0,0,0);">phoneme</span></td><td><span style="color:rgb(0,0,0);">字符串</span></td><td><span style="color:rgb(0,0,0);">音素的发音</span></td></tr><tr><td>pronunciation</td><td>整型或浮点型</td><td>音素发音得分。<span style="color:rgb(230,76,76);">若 precision（精度）设置为浮点型时，该值显示为浮点型（若该值小数 点后为 0，则显示成整型），若 scale（分 制）设置为（0,1）, 该值显示为浮点型， 否则为整型。</span></td></tr><tr><td>stress_mark</td><td>整型</td><td>该音素前的重<span style="color:rgb(0,0,0);">(zhòng)</span>音符号信息。0表示该音素前无重<span style="color:rgb(0,0,0);">(zhòng)</span>音符号、1表示该音素前有重<span style="color:rgb(0,0,0);">(zhòng)</span>音符号（ˈ）、2表示该音素前有次重<span style="color:rgb(0,0,0);">(zhòng)</span>音符号（ˌ）</td></tr></tbody></table></figure><p>&nbsp;</p><p><span style="color:rgb(0,0,0);"><strong>result-&gt;words-&gt; phonemes-&gt;span</strong></span><span style="color:hsl(0,75%,60%);"><strong>（</strong>当存在readType字段且值为4时，无该字段<strong>）</strong></span></p><figure class="table"><table><tbody><tr><td style="text-align:center;"><strong>属性</strong></td><td style="text-align:center;"><strong>类型</strong></td><td style="text-align:center;"><strong>含义</strong></td></tr><tr><td><span style="color:rgb(0,0,0);">end</span></td><td>整型</td><td><span style="color:rgb(0,0,0);">音素在音轨上的结束时间，单位：10毫秒</span></td></tr><tr><td>start</td><td>整型</td><td><span style="color:rgb(0,0,0);">音素在音轨上的开始时间，单位：10毫秒</span></td></tr></tbody></table></figure><p>&nbsp;</p><p><span style="color:rgb(0,0,0);"><strong>result-&gt;words→pause</strong></span><span style="color:hsl(0,75%,60%);"><strong>（</strong>当存在readType字段且值为4时，无该字段<strong>）</strong></span></p><figure class="table"><table><tbody><tr><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>属性</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>类型</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>含义</strong></span></td></tr><tr><td><span style="color:rgb(0,0,0);">duration</span></td><td>整型</td><td>停顿的时长，单位：10毫秒</td></tr><tr><td>type</td><td>整型</td><td><span style="color:rgb(0,0,0);">duration&gt;10该值为</span>1，否则值为0&nbsp;</td></tr></tbody></table></figure><p>&nbsp;</p><p><span style="color:rgb(0,0,0);"><strong>result-&gt;words-&gt;phonics</strong></span><span style="color:hsl(0,75%,60%);"><strong>（</strong>当存在readType字段且值为4时，无该字段<strong>）</strong></span></p><figure class="table"><table><tbody><tr><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>属性</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>类型</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>含义</strong></span></td></tr><tr><td><span style="color:rgb(0,0,0);">overall</span></td><td><span style="color:rgb(0,0,0);">整型或浮点型</span></td><td><span style="color:rgb(0,0,0);">音素总分</span><span style="color:rgb(57,57,57);">，</span><span style="color:rgb(230,76,76);">若 precision（精度）设置为浮点型时，该值显示为浮点型（若该值小数 点后为 0，则显示成整型），若 scale（分 制）设置为（0,1）, 该值显示为浮点型， 否则为整型。</span></td></tr><tr><td><span style="color:rgb(0,0,0);">phoneme</span></td><td><span style="color:rgb(0,0,0);">字符串数组</span></td><td><span style="color:rgb(0,0,0);">音素</span></td></tr><tr><td>spell</td><td>字符串</td><td>单词拼写</td></tr></tbody></table></figure><p>&nbsp;</p><h4><span style="color:rgb(0,0,0);"><strong>result-&gt;liaison（对象数组，需判断该字段是否存在）</strong></span></h4><figure class="table"><table><tbody><tr><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>属性</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>类型</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>含义</strong></span></td></tr><tr><td><span style="color:rgb(0,0,0);">first&nbsp;</span></td><td><span style="color:rgb(0,0,0);">对象</span></td><td><span style="color:rgb(0,0,0);">连读的第一个单词信息</span></td></tr><tr><td><span style="color:rgb(0,0,0);">second&nbsp;</span></td><td><span style="color:rgb(0,0,0);">对象</span></td><td><span style="color:rgb(0,0,0);">连读的第二个单词信息</span></td></tr><tr><td>first_phoneme</td><td>字符串</td><td>第一个单词的最后一个音素，音素类型同dict_type参数指定</td></tr><tr><td>second_phoneme</td><td>字符串</td><td>第二个单词的第一个音素，音素类型同dict_type参数指定</td></tr><tr><td>linkable_type</td><td>整型</td><td><p>本单词与下一个单词的连读类型。</p><p>-1：不可连读</p><p>0：辅音 + 元音</p><p>1：该单词的末尾和下一个单词的开始的音素都是同一个辅音</p><p>2：辅音 + 半元音</p><p>3：失去爆破</p><p>4：元音 + 元音</p><p>5：辅音 + him/her</p><p>6：/ɚ/&nbsp;+&nbsp;元音</p><p>7：元音 + him/her</p></td></tr></tbody></table></figure><p>&nbsp;</p><p><span style="color:rgb(0,0,0);"><strong>result- &gt; liaison &nbsp;-&gt;first</strong></span></p><figure class="table"><table><tbody><tr><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>属性</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>类型</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>含义</strong></span></td></tr><tr><td><span style="color:rgb(0,0,0);">Index&nbsp;</span></td><td>整型</td><td><span style="color:rgb(0,0,0);">本单词的位置信息</span></td></tr><tr><td><span style="color:rgb(0,0,0);">word &nbsp;</span></td><td><span style="color:rgb(0,0,0);">字符串</span></td><td><span style="color:rgb(0,0,0);">本单词文本</span></td></tr></tbody></table></figure><p>&nbsp;</p><p><span style="color:rgb(0,0,0);"><strong>result- &gt; liaison- &gt;&nbsp;second</strong></span></p><figure class="table"><table><tbody><tr><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>属性</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>类型</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>含义</strong></span></td></tr><tr><td><span style="color:rgb(0,0,0);">Index&nbsp;</span></td><td>整型</td><td><span style="color:rgb(0,0,0);">本单词的位置信息</span></td></tr><tr><td><span style="color:rgb(0,0,0);">word &nbsp;</span></td><td><span style="color:rgb(0,0,0);">字符串</span></td><td><span style="color:rgb(0,0,0);">本单词文本</span></td></tr></tbody></table></figure><h4>&nbsp;</h4><h4><span style="color:rgb(0,0,0);"><strong>result-&gt;plosion（对象数组，需判断该字段是否存在）</strong></span></h4><figure class="table"><table><tbody><tr><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>属性</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>类型</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>含义</strong></span></td></tr><tr><td><span style="color:rgb(0,0,0);">first&nbsp;</span></td><td><span style="color:rgb(0,0,0);">对象</span></td><td><span style="color:rgb(0,0,0);">爆破音的第一个单词信息</span></td></tr><tr><td><span style="color:rgb(0,0,0);">second&nbsp;</span></td><td><span style="color:rgb(0,0,0);">对象</span></td><td><span style="color:rgb(0,0,0);">爆破音的第二个单词信息</span></td></tr><tr><td>first_phoneme</td><td>字符串</td><td>第一个单词的最后一个音素，音素类型同dict_type参数指定</td></tr><tr><td>second_phoneme</td><td>字符串</td><td>第二个单词的第一个音素，音素类型同dict_type参数指定</td></tr><tr><td>linkable_type</td><td>整型</td><td><p>本单词与下一个单词的连读类型。</p><p>-1：不可连读</p><p>0：辅音 + 元音</p><p>1：该单词的末尾和下一个单词的开始的音素都是同一个辅音</p><p>2：辅音 + 半元音</p><p>3：失去爆破</p><p>4：元音 + 元音</p><p>5：辅音 + him/her</p><p>6：/ɚ/+&nbsp;元音</p><p>7：元音 + him/her</p></td></tr></tbody></table></figure><p>&nbsp;</p><p><span style="color:rgb(0,0,0);"><strong>result- &gt;plosion &nbsp;-&gt;first</strong></span></p><figure class="table"><table><tbody><tr><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>属性</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>类型</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>含义</strong></span></td></tr><tr><td><span style="color:rgb(0,0,0);">Index&nbsp;</span></td><td>整型</td><td><span style="color:rgb(0,0,0);">本单词的位置信息</span></td></tr><tr><td><span style="color:rgb(0,0,0);">word &nbsp;</span></td><td><span style="color:rgb(0,0,0);">字符串</span></td><td><span style="color:rgb(0,0,0);">本单词文本</span></td></tr></tbody></table></figure><h4>&nbsp;</h4><p><span style="color:rgb(0,0,0);"><strong>result- &gt;plosion &nbsp;-&gt;second</strong></span></p><figure class="table"><table><tbody><tr><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>属性</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>类型</strong></span></td><td style="text-align:center;"><span style="color:rgb(0,0,0);"><strong>含义</strong></span></td></tr><tr><td><span style="color:rgb(0,0,0);">Index&nbsp;</span></td><td>整型</td><td><span style="color:rgb(0,0,0);">本单词的位置信息</span></td></tr><tr><td><span style="color:rgb(0,0,0);">word &nbsp;</span></td><td><span style="color:rgb(0,0,0);">字符串</span></td><td><span style="color:rgb(0,0,0);">本单词文本</span></td></tr></tbody></table></figure><h4>&nbsp;</h4><h4>&nbsp;</h4>

                        <!-- <div id="editor" style="height:100%"></div> -->
                    </div>
                    <div id="catalog" class="col-md-2"></div>
                </div>
            </div>
        </div>
    </div>
</div>

                
			<div class="footer">
				<div class="footer-inner">
					<!-- #section:basics/footer -->
					<div class="footer-content">
						<span class="bigger-120">
							<span class="blue bolder">声通科技</span>
							&copy; 2025
						</span>
					</div>

					<!-- /section:basics/footer -->
				</div>
			</div>

			<a href="#" id="btn-scroll-up" class="btn-scroll-up btn btn-sm btn-inverse">
				<i class="ace-icon fa fa-angle-double-up icon-only bigger-110"></i>
			</a>
		</div><!-- /.main-container -->

		<!-- basic scripts -->

		<!--[if !IE]> -->
		<script src="/assets/js/jquery-2.2.4.min.js"></script>

		<!-- <![endif]-->

		<!--[if IE]>
        <script src="/components/jquery.1x/dist/jquery.js"></script>
        <![endif]-->
		<script type="text/javascript">
			if('ontouchstart' in document.documentElement) document.write("<script src='../components/_mod/jquery.mobile.custom/jquery.mobile.custom.js'>"+"<"+"/script>");
		</script>
		<script src="/components/bootstrap/dist/js/bootstrap.js"></script>

		<!-- page specific plugin scripts -->

		<!--[if lte IE 8]>
		  <script src="/components/ExplorerCanvas/excanvas.js"></script>
		<![endif]-->

        <!-- ace scripts -->
        
        <script src="/assets/js/ace-elements.min.js"></script>
		<script src="/assets/js/ace.min.js"></script>
		<script src="/components/jquery.gritter/js/jquery.gritter.min.js"></script>
		<script src="/components/bootbox/bootbox.all.min.js"></script>
		<script src="/assets/js/app.min.js"></script>
		<script src="/assets/js/search.min.js"></script>
		<!-- inline scripts related to this page -->
		<script type="text/JavaScript">
			jQuery(function($){
				//$(".nav-list .active").parent().parent().addClass("active open");

				$("a[data-action=edit-user]").on('click', function(){
					$this = $(this);
					var id = $this.data("id");
					var name = $this.data("name");
					var email = $this.data("email");
					dialog= bootbox.dialog({
						title: '编辑',
						buttons: {
							cancel: {
								label: "取消",
								className: 'btn-danger',
							},
							ok: {
								label: "确定",
								className: 'btn-info',
								callback: function(){
									$.ajax({
										type: "POST",
										url: "/users/edit_user",
										data: $("#user-form").serialize(),
										beforeSend: function() {
											var flag = true;
											$('#user-form input[required*=""]').each(function(){
												if($(this).val() == "") {
													$(this).focus();
													notice("","参数不能为空","warning");
													flag = false;
													return false;
												}
											});
											let password1 = $.trim($('input[name="password1"]').val());
											let password2 = $.trim($('input[name="password2"]').val());
											if ((password1 != '' || password2 != '') && password1!=password2) {
												notice("","两次输入密码不一致","warning");
												flag = false;
											}
											if(!flag) {
												return flag;
											}
											
										},
										success: function(data) {
											try {
												var obj = JSON.parse(data);
												if(obj.status == "ok") {
													$('.user-info span').text($('#user-form input[name=name]').val());
													$this.attr('data-name', $('#user-form input[name=name]').val());
													$this.attr('data-email', $('#user-form input[name=email]').val());
													notice("","保存成功","success");
													dialog.modal('hide');
												} else {
													notice("保存失败",obj.error,"error");
												}
											}catch(e) {
												notice("保存失败","","error");
											}
											
										},
										error: function(jqXHR, textStatus, errorThrown){
											console.log(jqXHR);
											console.log(textStatus);
											console.log(errorThrown);
											notice("保存失败",jqXHR.status + ':' + errorThrown,"error");
										},
										complete: function() {
											
										}
									})
									return false;
								}
							}
						},
						message: '<div class="row">'+
								'<div class="col-xs-12">'+
								'<form class="form-horizontal" id="user-form" role="form">' +
								'<div class="form-group">'+
								'<label class="col-sm-3 control-label no-padding-right required"> 昵称: </label>'+
								'<div class="col-sm-9">'+
								'<input type="text"  name="id" class="hidden" value="' + id + '"/>'+
								'<input type="text"  name="name" class="col-xs-10 col-sm-6" required value="' + name + '"/>'+
								'</div>'+
								'</div>'+
								'<div class="form-group">'+
								'<label class="col-sm-3 control-label no-padding-right required"> 邮箱: </label>'+
								'<div class="col-sm-9">'+
								'<input type="text"  name="email" class="col-xs-10 col-sm-6" required value="' + email + '"/>'+
								'</div>'+
								'</div>'+
								'<div class="form-group">'+
								'<label class="col-sm-3 control-label no-padding-right"> 密码: </label>'+
								'<div class="col-sm-9">'+
								'<input type="password" class="input-md" name="password1"/>&nbsp;'+
								'<input type="password" class="input-md" name="password2"/>'+
								'</div>'+
								'</div>'+
								'</form>'+
								'</div>'+
								'</div>',
					});
				});
			});
		</script>
		<script src="/assets/js/catalog.min.js"></script>
<script type="text/JavaScript">	
    jQuery(function($){
        doMenu($('#content'), $('#catalog'));
        $('table').addClass('table table-striped table-bordered table-hover');



            
        $('button[data-action="favourite"').on('click', function() {
            $this = $(this);
            let id = $(this).data('id');
            $.ajax({
                type: "POST",
                url: 'favourite',
                data: {id:id},
                beforeSend: function() {},
                success: function(data) {
                    try {
                        var obj = JSON.parse(data);
                        if(obj.status == "ok") {
                            let txt = $this.html();
                            if(txt=='已关注') {
                                $this.html('关注')
                                notice("","已取消关注","success");
                            } else {
                                $this.html('已关注');
                                if(obj.hasOwnProperty('error')) {
                                    notice("","已关注，" + obj.error,"warning");
                                }else{
                                    notice("","已关注","success");
                                }
                                
                            }
                        } else {
                            notice("操作失败",obj.error,"error");
                        }
                    }catch(e) {
                        console.log(e);
                        notice("操作失败","","error");
                    }
                    
                },
                error: function(jqXHR, textStatus, errorThrown){
                    console.log(jqXHR);
                    console.log(textStatus);
                    console.log(errorThrown);
                    notice("操作失败","","error");
                },
                complete: function() {
                }
            });
        })

    }); 
</script>

		
	</body>
</html>

{"applicationId": "1752032229000537", "dtLastResponse": "2025-07-10 17:12:49:432", "eof": 1, "params": {"app": {"applicationId": "1752032229000537", "sig": "281a591b8b2eee6694769a5348d9a04068f86c8f", "timestamp": "1752138763", "userId": "1941834967734571008"}, "audio": {"audioType": "opus", "channel": 1, "compress": "speex", "sampleBytes": 2, "sampleRate": 16000}, "coreProvideType": "cloud", "request": {"coreType": "para.eval", "refText": "Android dependency should be downloaded from Maven", "tokenId": "686f840b332793774b000011"}}, "recordId": "686f840cb631ff1f000287d0", "refText": "Android dependency should be downloaded from Maven", "result": {"duration": "5.039", "fluency": 100, "integrity": 29, "kernel_version": "7.5.4", "numeric_duration": 5.039, "overall": 12, "pronunciation": 43, "resource_version": "5.2.0", "rhythm": 57, "sentences": [{"beginIndex": 0, "end": 0, "endIndex": 49, "overall": 12, "sentence": "Android dependency should be downloaded from Maven", "start": 114}], "speed": 103, "warning": [{"code": 1004, "message": "Audio noisy!"}]}, "tokenId": "686f840b332793774b000011"}